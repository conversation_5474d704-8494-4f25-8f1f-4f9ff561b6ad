# 依赖目录
node_modules/
dist/

# 本地环境文件
.env.local
.env.*.local

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统文件
.DS_Store
Thumbs.db

# 构建输出
/dist
/build

# 本地配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存文件
.cache
.temp

# 测试覆盖率
/coverage

# 包管理器文件
package-lock.json
yarn.lock
pnpm-lock.yaml 