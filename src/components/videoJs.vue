<template>
  <video ref="videoPlayer" playsInline class="video-js vjs-big-play-centered" style="width: 100%; height: 100%; objectFit:cover;"></video>
</template>

<script>
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
export default {
  props: {
    options: {
      type: Object,
      default () {
        return {
          preload: 'auto',
          controls: false,
          autoplay: false,
          loop: false
        }
      }
    }
  },
  data () {
    return {
      player: null
    }
  },
  mounted () {
    this.player = videojs(this.$refs.videoPlayer, this.options, () => {
      // console.log('onPlayerReady', this)
      this.player.playsinline(true)
    })
  },
  beforeDestroy () {
    if (this.player) {
      this.player.dispose()
    }
  },
  methods: {
    play () {
      this.player.play()
    },
    pause () {
      this.player.pause()
    },
    getPlayer () {
      return this.player
    },
    async setProgressCurrentTime (progressTimestamp, play) {
      var currentTime = this.player && this.player.currentTime()
      if (!currentTime) return
      if (Math.abs(currentTime * 1000 - progressTimestamp) > 1000) {
        this.player && this.player.currentTime(progressTimestamp / 1000)
      } else {
        // play && play.pause()
        // await this.player && this.player.play()
        // play && play.play()
      }
    },
    async setCurrentTime (currentTime, play) {
      this.player && this.player.currentTime(currentTime)
      await this.player && this.player.play()
      play.play()
    },
    // 设置当前起始时间不自动播放
    async setProgressCurrTime (progressTimestamp) {
      this.player && this.player.currentTime(progressTimestamp / 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.video-js {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  overflow: hidden;
  /deep/ .vjs-big-play-button{
      font-size: 2.5em;
      line-height: 2.3em;
      width: 2.5em;
      height: 2.5em;
      -webkit-border-radius: 2.5em;
      -moz-border-radius: 2.5em;
      border-radius: 2.5em;
      background-color: #73859f;
      background-color: rgba(115,133,159,.5);
      margin-top: -1.25em;
      margin-left: -1.75em;
  }
  /* 中间的播放箭头 */
  /deep/ .vjs-big-play-button .vjs-icon-placeholder {
      font-size: 1.63em;
  }
  /* 加载圆圈 */
  /deep/ .vjs-loading-spinner {
      font-size: 2.5em;
      height: 2em;
      border-radius: 1em;
      margin-top: -1em;
      margin-left: -1.5em;
  }
}

</style>
