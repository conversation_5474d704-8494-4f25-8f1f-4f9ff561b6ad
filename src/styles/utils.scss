// Box Sizing
.box-border {
  box-sizing: border-box;
}

.box-content {
  box-sizing: content-box;
}

// Display
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.grid {
  display: grid;
}

.inline-grid {
  display: inline-grid;
}

.hidden {
  display: none;
}

// Object Fit
.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.object-fill {
  object-fit: fill;
}

.object-none {
  object-fit: none;
}

.object-scale-down {
  object-fit: scale-down;
}

// Overflow
.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-visible {
  overflow-x: visible;
}

.overflow-y-visible {
  overflow-y: visible;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

// Position
.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

// Visibility
.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

// Flex Direction

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

// Flex Wrap
.flex-wrap {
  flex-wrap: wrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

// Flex Grow
.flex-grow-0 {
  flex-grow: 0;
}

.flex-grow {
  flex-grow: 1;
}

// Flex Shrink
.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-1 {
  flex: 1;
}

// Justify Content
.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

// Justify Items
.justify-items-start {
  justify-items: flex-start;
}

.justify-items-end {
  justify-items: flex-end;
}

.justify-items-center {
  justify-items: center;
}

.justify-items-stretch {
  justify-items: stretch;
}

// Justify Self
.justify-self-auto {
  justify-self: auto;
}

.justify-self-start {
  justify-self: flex-start;
}

.justify-self-end {
  justify-self: flex-end;
}

.justify-self-center {
  justify-self: center;
}

.justify-self-stretch {
  justify-self: stretch;
}

// Align Content
.align-content-center {
  align-content: center;
}

.align-content-start {
  align-content: flex-start;
}

.align-content-end {
  align-content: flex-end;
}

.align-content-between {
  align-content: space-between;
}

.align-content-around {
  align-content: space-around;
}

.align-content-evenly {
  align-content: space-evenly;
}

//Align Items
.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-baseline {
  align-items: baseline;
}

.align-stretch {
  align-items: stretch;
}

// Align Self
.align-self-auto {
  align-self: auto;
}

.align-self-start {
  align-self: flex-start;
}

.align-self-end {
  align-self: flex-end;
}

.align-self-center {
  align-self: center;
}

.align-self-stretch {
  align-self: stretch;
}

// Grid Template Columns
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}

.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}

.grid-cols-11 {
  grid-template-columns: repeat(11, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-none {
  grid-template-columns: none;
}

// Grid Column Start / End
.col-auto {
  grid-column: auto;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-7 {
  grid-column: span 7 / span 7;
}

.col-span-8 {
  grid-column: span 8 / span 8;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-span-10 {
  grid-column: span 10 / span 10;
}

.col-span-11 {
  grid-column: span 11 / span 11;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-full {
  grid-column: 1 / -1;
}

.col-start-1 {
  grid-column-start: 1;
}

.col-start-2 {
  grid-column-start: 2;
}

.col-start-3 {
  grid-column-start: 3;
}

.col-start-4 {
  grid-column-start: 4;
}

.col-start-5 {
  grid-column-start: 5;
}

.col-start-6 {
  grid-column-start: 6;
}

.col-start-7 {
  grid-column-start: 7;
}

.col-start-8 {
  grid-column-start: 8;
}

.col-start-9 {
  grid-column-start: 9;
}

.col-start-10 {
  grid-column-start: 10;
}

.col-start-11 {
  grid-column-start: 11;
}

.col-start-12 {
  grid-column-start: 12;
}

.col-start-13 {
  grid-column-start: 13;
}

.col-start-auto {
  grid-column-start: auto;
}

.col-end-1 {
  grid-column-end: 1;
}

.col-end-2 {
  grid-column-end: 2;
}

.col-end-3 {
  grid-column-end: 3;
}

.col-end-4 {
  grid-column-end: 4;
}

.col-end-5 {
  grid-column-end: 5;
}

.col-end-6 {
  grid-column-end: 6;
}

.col-end-7 {
  grid-column-end: 7;
}

.col-end-8 {
  grid-column-end: 8;
}

.col-end-9 {
  grid-column-end: 9;
}

.col-end-10 {
  grid-column-end: 10;
}

.col-end-11 {
  grid-column-end: 11;
}

.col-end-12 {
  grid-column-end: 12;
}

.col-end-13 {
  grid-column-end: 13;
}

.col-end-auto {
  grid-column-end: auto;
}

// Grid Template Rows
.grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr));
}

.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.grid-rows-3 {
  grid-template-rows: repeat(3, minmax(0, 1fr));
}

.grid-rows-4 {
  grid-template-rows: repeat(4, minmax(0, 1fr));
}

.grid-rows-5 {
  grid-template-rows: repeat(5, minmax(0, 1fr));
}

.grid-rows-6 {
  grid-template-rows: repeat(6, minmax(0, 1fr));
}

.grid-rows-none {
  grid-template-rows: none;
}

//Grid Row Start / End
.row-auto {
  grid-row: auto;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-span-4 {
  grid-row: span 4 / span 4;
}

.row-span-5 {
  grid-row: span 5 / span 5;
}

.row-span-6 {
  grid-row: span 6 / span 6;
}

.row-span-full {
  grid-row: 1 / -1;
}

.row-start-1 {
  grid-row-start: 1;
}

.row-start-2 {
  grid-row-start: 2;
}

.row-start-3 {
  grid-row-start: 3;
}

.row-start-4 {
  grid-row-start: 4;
}

.row-start-5 {
  grid-row-start: 5;
}

.row-start-6 {
  grid-row-start: 6;
}

.row-start-7 {
  grid-row-start: 7;
}

.row-start-auto {
  grid-row-start: auto;
}

.row-end-1 {
  grid-row-end: 1;
}

.row-end-2 {
  grid-row-end: 2;
}

.row-end-3 {
  grid-row-end: 3;
}

.row-end-4 {
  grid-row-end: 4;
}

.row-end-5 {
  grid-row-end: 5;
}

.row-end-6 {
  grid-row-end: 6;
}

.row-end-7 {
  grid-row-end: 7;
}

.row-end-auto {
  grid-row-end: auto;
}

// Whitespace
.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

// Word Break
.break-normal {
  overflow-wrap: normal;
  word-break: normal;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

/*长度高度*/
.w1 {
  width: 1px;
}
.w2 {
  width: 2px;
}
.w3 {
  width: 3px;
}
.w4 {
  width: 4px;
}
.w5 {
  width: 5px;
}
.w6 {
  width: 6px;
}
.w7 {
  width: 7px;
}
.w8 {
  width: 8px;
}
.w9 {
  width: 9px;
}
.w10 {
  width: 10px;
}
.w15 {
  width: 15px;
}
.w20 {
  width: 20px;
}
.w30 {
  width: 30px;
}
.w40 {
  width: 40px;
}
.w50 {
  width: 50px;
}
.w60 {
  width: 60px;
}
.w70 {
  width: 70px;
}
.w80 {
  width: 80px;
}
.w90 {
  width: 90px;
}
.w100 {
  width: 100px;
}
.w110 {
  width: 110px;
}
.w120 {
  width: 120px;
}
.w130 {
  width: 130px;
}
.w140 {
  width: 140px;
}
.w150 {
  width: 150px;
}
.w160 {
  width: 160px;
}
.w170 {
  width: 170px;
}
.w180 {
  width: 180px;
}
.w190 {
  width: 190px;
}
.w200 {
  width: 200px;
}
.w210 {
  width: 210px;
}
.w220 {
  width: 220px;
}
.w230 {
  width: 230px;
}
.w240 {
  width: 240px;
}
.w250 {
  width: 250px;
}
.w260 {
  width: 260px;
}
.w270 {
  width: 270px;
}
.w280 {
  width: 280px;
}
.w290 {
  width: 290px;
}
.w300 {
  width: 300px;
}
.w310 {
  width: 310px;
}
.w320 {
  width: 320px;
}
.w330 {
  width: 330px;
}
.w340 {
  width: 340px;
}
.w350 {
  width: 350px;
}
.w360 {
  width: 360px;
}
.w370 {
  width: 370px;
}
.w380 {
  width: 380px;
}
.w390 {
  width: 390px;
}
.w400 {
  width: 400px;
}
.w410 {
  width: 410px;
}
.w420 {
  width: 420px;
}
.w430 {
  width: 430px;
}
.w440 {
  width: 440px;
}
.w450 {
  width: 450px;
}
.w460 {
  width: 460px;
}
.w470 {
  width: 470px;
}
.w480 {
  width: 480px;
}
.w490 {
  width: 490px;
}
.w500 {
  width: 500px;
}
.w510 {
  width: 510px;
}
.w520 {
  width: 520px;
}
.w530 {
  width: 530px;
}
.w540 {
  width: 540px;
}
.w550 {
  width: 550px;
}
.w560 {
  width: 560px;
}
.w570 {
  width: 570px;
}
.w580 {
  width: 580px;
}
.w590 {
  width: 590px;
}
.w600 {
  width: 600px;
}
.w610 {
  width: 610px;
}
.w620 {
  width: 620px;
}
.w630 {
  width: 630px;
}
.w640 {
  width: 640px;
}
.w650 {
  width: 650px;
}
.w660 {
  width: 660px;
}
.w670 {
  width: 670px;
}
.w680 {
  width: 680px;
}
.w690 {
  width: 690px;
}
.w700 {
  width: 700px;
}
.w710 {
  width: 710px;
}
.w720 {
  width: 720px;
}
.w730 {
  width: 730px;
}
.w740 {
  width: 740px;
}
.w750 {
  width: 750px;
}
.w760 {
  width: 760px;
}
.w770 {
  width: 770px;
}
.w780 {
  width: 780px;
}
.w790 {
  width: 790px;
}
.w800 {
  width: 800px;
}
.w810 {
  width: 810px;
}
.w820 {
  width: 820px;
}
.w830 {
  width: 830px;
}
.w840 {
  width: 840px;
}
.w850 {
  width: 850px;
}
.w860 {
  width: 860px;
}
.w870 {
  width: 870px;
}
.w880 {
  width: 880px;
}
.w890 {
  width: 890px;
}
.w900 {
  width: 900px;
}
.w910 {
  width: 910px;
}
.w920 {
  width: 920px;
}
.w930 {
  width: 930px;
}
.w940 {
  width: 940px;
}
.w950 {
  width: 950px;
}
.w960 {
  width: 960px;
}
.w970 {
  width: 970px;
}
.w980 {
  width: 980px;
}
.w990 {
  width: 990px;
}
.w1000 {
  width: 1000px;
}
.w {
  width: 100%;
}
.w-screen {
  width: 100vw;
}

.h0 {
  height: 0px;
}
.h1 {
  height: 1px;
}
.h2 {
  height: 2px;
}
.h3 {
  height: 3px;
}
.h4 {
  height: 4px;
}
.h5 {
  height: 5px;
}
.h6 {
  height: 6px;
}
.h7 {
  height: 7px;
}
.h8 {
  height: 8px;
}
.h9 {
  height: 9px;
}
.h10 {
  height: 10px;
}
.h12 {
  height: 12px;
}
.h14 {
  height: 14px;
}
.h16 {
  height: 16px;
}
.h18 {
  height: 18px;
}
.h20 {
  height: 20px;
}
.h22 {
  height: 22px;
}
.h24 {
  height: 25px;
}
.h26 {
  height: 26px;
}
.h28 {
  height: 28px;
}
.h30 {
  height: 30px;
}
.h32 {
  height: 32px;
}
.h34 {
  height: 34px;
}
.h36 {
  height: 36px;
}
.h38 {
  height: 38px;
}
.h40 {
  height: 40px;
}
.h42 {
  height: 42px;
}
.h44 {
  height: 44px;
}
.h46 {
  height: 46px;
}
.h48 {
  height: 48px;
}
.h50 {
  height: 50px;
}
.h52 {
  height: 52px;
}
.h54 {
  height: 54px;
}
.h56 {
  height: 56px;
}
.h58 {
  height: 58px;
}
.h60 {
  height: 60px;
}
.h70 {
  height: 70px;
}
.h80 {
  height: 80px;
}
.h90 {
  height: 90px;
}
.h100 {
  height: 100px;
}
.h120 {
  height: 120px;
}
.h140 {
  height: 140px;
}
.h160 {
  height: 160px;
}
.h180 {
  height: 180px;
}
.h200 {
  height: 200px;
}
.h240 {
  height: 240px;
}
.h300 {
  height: 300px;
}
.h400 {
  height: 400px;
}
.h {
  height: 100%;
}
.h-screen {
  height: 100vh;
}

/*边距*/
.m1 {
  margin: 1px;
}
.m2 {
  margin: 2px;
}
.m3 {
  margin: 3px;
}
.m4 {
  margin: 4px;
}
.m5 {
  margin: 5px;
}
.m6 {
  margin: 6px;
}
.m7 {
  margin: 7px;
}
.m8 {
  margin: 8px;
}
.m9 {
  margin: 9px;
}
.m10 {
  margin: 10px;
}
.m11 {
  margin: 11px;
}
.m12 {
  margin: 12px;
}
.m13 {
  margin: 13px;
}
.m14 {
  margin: 14px;
}
.m15 {
  margin: 15px;
}
.m16 {
  margin: 16px;
}
.m17 {
  margin: 17px;
}
.m18 {
  margin: 18px;
}
.m19 {
  margin: 19px;
}
.m20 {
  margin: 20px;
}
.m22 {
  margin: 22px;
}
.m24 {
  margin: 23px;
}
.m26 {
  margin: 26px;
}
.m28 {
  margin: 28px;
}
.m30 {
  margin: 30px;
}
.m35 {
  margin: 35px;
}
.m40 {
  margin: 40px;
}
.m50 {
  margin: 40px;
}
.m60 {
  margin: 60px;
}

.mt1 {
  margin-top: 1px;
}
.mt2 {
  margin-top: 2px;
}
.mt3 {
  margin-top: 3px;
}
.mt4 {
  margin-top: 4px;
}
.mt5 {
  margin-top: 5px;
}
.mt6 {
  margin-top: 6px;
}
.mt7 {
  margin-top: 7px;
}
.mt8 {
  margin-top: 8px;
}
.mt9 {
  margin-top: 9px;
}
.mt10 {
  margin-top: 10px;
}
.mt11 {
  margin-top: 11px;
}
.mt12 {
  margin-top: 12px;
}
.mt13 {
  margin-top: 13px;
}
.mt14 {
  margin-top: 14px;
}
.mt15 {
  margin-top: 15px;
}
.mt16 {
  margin-top: 16px;
}
.mt17 {
  margin-top: 17px;
}
.mt18 {
  margin-top: 18px;
}
.mt19 {
  margin-top: 19px;
}
.mt20 {
  margin-top: 20px;
}
.mt22 {
  margin-top: 22px;
}
.mt24 {
  margin-top: 24px;
}
.mt26 {
  margin-top: 26px;
}
.mt28 {
  margin-top: 28px;
}
.mt30 {
  margin-top: 30px;
}
.mt32 {
  margin-top: 32px;
}
.mt35 {
  margin-top: 35px;
}
.mt38 {
  margin-top: 38px;
}
.mt40 {
  margin-top: 40px;
}
.mt42 {
  margin-top: 42px;
}
.mt45 {
  margin-top: 45px;
}
.mt48 {
  margin-top: 48px;
}
.mt50 {
  margin-top: 50px;
}
.mt60 {
  margin-top: 60px;
}
.mt70 {
  margin-top: 70px;
}
.mt80 {
  margin-top: 80px;
}
.mt90 {
  margin-top: 90px;
}
.mt100 {
  margin-top: 100px;
}

.mb1 {
  margin-bottom: 1px;
}
.mb2 {
  margin-bottom: 2px;
}
.mb3 {
  margin-bottom: 3px;
}
.mb4 {
  margin-bottom: 4px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb6 {
  margin-bottom: 6px;
}
.mb7 {
  margin-bottom: 7px;
}
.mb8 {
  margin-bottom: 8px;
}
.mb9 {
  margin-bottom: 9px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  margin-bottom: 11px;
}
.mb12 {
  margin-bottom: 12px;
}
.mb13 {
  margin-bottom: 13px;
}
.mb14 {
  margin-bottom: 14px;
}
.mb15 {
  margin-bottom: 15px;
}
.mb16 {
  margin-bottom: 16px;
}
.mb17 {
  margin-bottom: 17px;
}
.mb18 {
  margin-bottom: 18px;
}
.mb19 {
  margin-bottom: 19px;
}
.mb20 {
  margin-bottom: 20px;
}
.mb22 {
  margin-bottom: 22px;
}
.mb24 {
  margin-bottom: 24px;
}
.mb26 {
  margin-bottom: 26px;
}
.mb28 {
  margin-bottom: 28px;
}
.mb30 {
  margin-bottom: 30px;
}
.mb32 {
  margin-bottom: 32px;
}
.mb35 {
  margin-bottom: 35px;
}
.mb38 {
  margin-bottom: 38px;
}
.mb40 {
  margin-bottom: 40px;
}
.mb42 {
  margin-bottom: 42px;
}
.mb45 {
  margin-bottom: 45px;
}
.mb48 {
  margin-bottom: 48px;
}
.mb50 {
  margin-bottom: 50px;
}
.mb60 {
  margin-bottom: 60px;
}
.mb70 {
  margin-bottom: 70px;
}
.mb80 {
  margin-bottom: 80px;
}
.mb90 {
  margin-bottom: 90px;
}
.mb100 {
  margin-bottom: 100px;
}

.ml1 {
  margin-left: 1px;
}
.ml2 {
  margin-left: 2px;
}
.ml3 {
  margin-left: 3px;
}
.ml4 {
  margin-left: 4px;
}
.ml5 {
  margin-left: 5px;
}
.ml6 {
  margin-left: 6px;
}
.ml7 {
  margin-left: 7px;
}
.ml8 {
  margin-left: 8px;
}
.ml9 {
  margin-left: 9px;
}
.ml10 {
  margin-left: 10px;
}
.ml11 {
  margin-left: 11px;
}
.ml12 {
  margin-left: 12px;
}
.ml13 {
  margin-left: 13px;
}
.ml14 {
  margin-left: 14px;
}
.ml15 {
  margin-left: 15px;
}
.ml16 {
  margin-left: 16px;
}
.ml17 {
  margin-left: 17px;
}
.ml18 {
  margin-left: 18px;
}
.ml19 {
  margin-left: 19px;
}
.ml20 {
  margin-left: 20px;
}
.ml22 {
  margin-left: 22px;
}
.ml24 {
  margin-left: 24px;
}
.ml26 {
  margin-left: 26px;
}
.ml28 {
  margin-left: 28px;
}
.ml30 {
  margin-left: 30px;
}
.ml32 {
  margin-left: 32px;
}
.ml35 {
  margin-left: 35px;
}
.ml38 {
  margin-left: 38px;
}
.ml40 {
  margin-left: 40px;
}
.ml42 {
  margin-left: 42px;
}
.ml45 {
  margin-left: 45px;
}
.ml48 {
  margin-left: 48px;
}
.ml50 {
  margin-left: 50px;
}
.ml60 {
  margin-left: 60px;
}
.ml70 {
  margin-left: 70px;
}
.ml80 {
  margin-left: 80px;
}
.ml90 {
  margin-left: 90px;
}
.ml100 {
  margin-left: 100px;
}

.mr1 {
  margin-right: 1px;
}
.mr2 {
  margin-right: 2px;
}
.mr3 {
  margin-right: 3px;
}
.mr4 {
  margin-right: 4px;
}
.mr5 {
  margin-right: 5px;
}
.mr6 {
  margin-right: 6px;
}
.mr7 {
  margin-right: 7px;
}
.mr8 {
  margin-right: 8px;
}
.mr9 {
  margin-right: 9px;
}
.mr10 {
  margin-right: 10px;
}
.mr11 {
  margin-right: 11px;
}
.mr12 {
  margin-right: 12px;
}
.mr13 {
  margin-right: 13px;
}
.mr14 {
  margin-right: 14px;
}
.mr15 {
  margin-right: 15px;
}
.mr16 {
  margin-right: 16px;
}
.mr17 {
  margin-right: 17px;
}
.mr18 {
  margin-right: 18px;
}
.mr19 {
  margin-right: 19px;
}
.mr20 {
  margin-right: 20px;
}
.mr22 {
  margin-right: 22px;
}
.mr24 {
  margin-right: 24px;
}
.mr26 {
  margin-right: 26px;
}
.mr28 {
  margin-right: 28px;
}
.mr30 {
  margin-right: 30px;
}
.mr32 {
  margin-right: 32px;
}
.mr35 {
  margin-right: 35px;
}
.mr38 {
  margin-right: 38px;
}
.mr40 {
  margin-right: 40px;
}
.mr42 {
  margin-right: 42px;
}
.mr45 {
  margin-right: 45px;
}
.mr48 {
  margin-right: 48px;
}
.mr50 {
  margin-right: 50px;
}
.mr60 {
  margin-right: 60px;
}
.mr70 {
  margin-right: 70px;
}
.mr80 {
  margin-right: 80px;
}
.mr90 {
  margin-right: 90px;
}
.mr100 {
  margin-right: 100px;
}

.p1 {
  padding: 1px;
}
.p2 {
  padding: 2px;
}
.p3 {
  padding: 3px;
}
.p4 {
  padding: 4px;
}
.p5 {
  padding: 5px;
}
.p6 {
  padding: 6px;
}
.p7 {
  padding: 7px;
}
.p8 {
  padding: 8px;
}
.p9 {
  padding: 9px;
}
.p10 {
  padding: 10px;
}
.p11 {
  padding: 11px;
}
.p12 {
  padding: 12px;
}
.p13 {
  padding: 13px;
}
.p14 {
  padding: 14px;
}
.p15 {
  padding: 15px;
}
.p16 {
  padding: 16px;
}
.p17 {
  padding: 17px;
}
.p18 {
  padding: 18px;
}
.p19 {
  padding: 19px;
}
.p20 {
  padding: 20px;
}
.p22 {
  padding: 22px;
}
.p24 {
  padding: 23px;
}
.p26 {
  padding: 26px;
}
.p28 {
  padding: 28px;
}
.p30 {
  padding: 30px;
}
.p35 {
  padding: 35px;
}
.p40 {
  padding: 40px;
}
.p50 {
  padding: 40px;
}
.p60 {
  padding: 60px;
}

.ptsafe {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.pt1 {
  padding-top: 1px;
}
.pt2 {
  padding-top: 2px;
}
.pt3 {
  padding-top: 3px;
}
.pt4 {
  padding-top: 4px;
}
.pt5 {
  padding-top: 5px;
}
.pt6 {
  padding-top: 6px;
}
.pt7 {
  padding-top: 7px;
}
.pt8 {
  padding-top: 8px;
}
.pt9 {
  padding-top: 9px;
}
.pt10 {
  padding-top: 10px;
}
.pt11 {
  padding-top: 11px;
}
.pt12 {
  padding-top: 12px;
}
.pt13 {
  padding-top: 13px;
}
.pt14 {
  padding-top: 14px;
}
.pt15 {
  padding-top: 15px;
}
.pt16 {
  padding-top: 16px;
}
.pt17 {
  padding-top: 17px;
}
.pt18 {
  padding-top: 18px;
}
.pt19 {
  padding-top: 19px;
}
.pt20 {
  padding-top: 20px;
}
.pt22 {
  padding-top: 22px;
}
.pt24 {
  padding-top: 24px;
}
.pt26 {
  padding-top: 26px;
}
.pt28 {
  padding-top: 28px;
}
.pt30 {
  padding-top: 30px;
}
.pt32 {
  padding-top: 32px;
}
.pt35 {
  padding-top: 35px;
}
.pt38 {
  padding-top: 38px;
}
.pt40 {
  padding-top: 40px;
}
.pt42 {
  padding-top: 42px;
}
.pt45 {
  padding-top: 45px;
}
.pt48 {
  padding-top: 48px;
}
.pt50 {
  padding-top: 50px;
}
.pt60 {
  padding-top: 60px;
}
.pt70 {
  padding-top: 70px;
}
.pt80 {
  padding-top: 80px;
}
.pt90 {
  padding-top: 90px;
}
.pt100 {
  padding-top: 100px;
}

.pbsafe {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.pb1 {
  padding-bottom: 1px;
}
.pb2 {
  padding-bottom: 2px;
}
.pb3 {
  padding-bottom: 3px;
}
.pb4 {
  padding-bottom: 4px;
}
.pb5 {
  padding-bottom: 5px;
}
.pb6 {
  padding-bottom: 6px;
}
.pb7 {
  padding-bottom: 7px;
}
.pb8 {
  padding-bottom: 8px;
}
.pb9 {
  padding-bottom: 9px;
}
.pb10 {
  padding-bottom: 10px;
}
.pb11 {
  padding-bottom: 11px;
}
.pb12 {
  padding-bottom: 12px;
}
.pb13 {
  padding-bottom: 13px;
}
.pb14 {
  padding-bottom: 14px;
}
.pb15 {
  padding-bottom: 15px;
}
.pb16 {
  padding-bottom: 16px;
}
.pb17 {
  padding-bottom: 17px;
}
.pb18 {
  padding-bottom: 18px;
}
.pb19 {
  padding-bottom: 19px;
}
.pb20 {
  padding-bottom: 20px;
}
.pb22 {
  padding-bottom: 22px;
}
.pb24 {
  padding-bottom: 24px;
}
.pb26 {
  padding-bottom: 26px;
}
.pb28 {
  padding-bottom: 28px;
}
.pb30 {
  padding-bottom: 30px;
}
.pb32 {
  padding-bottom: 32px;
}
.pb35 {
  padding-bottom: 35px;
}
.pb38 {
  padding-bottom: 38px;
}
.pb40 {
  padding-bottom: 40px;
}
.pb42 {
  padding-bottom: 42px;
}
.pb45 {
  padding-bottom: 45px;
}
.pb48 {
  padding-bottom: 48px;
}
.pb50 {
  padding-bottom: 50px;
}
.pb60 {
  padding-bottom: 60px;
}
.pb70 {
  padding-bottom: 70px;
}
.pb80 {
  padding-bottom: 80px;
}
.pb90 {
  padding-bottom: 90px;
}
.pb100 {
  padding-bottom: 100px;
}

.pl1 {
  padding-left: 1px;
}
.pl2 {
  padding-left: 2px;
}
.pl3 {
  padding-left: 3px;
}
.pl4 {
  padding-left: 4px;
}
.pl5 {
  padding-left: 5px;
}
.pl6 {
  padding-left: 6px;
}
.pl7 {
  padding-left: 7px;
}
.pl8 {
  padding-left: 8px;
}
.pl9 {
  padding-left: 9px;
}
.pl10 {
  padding-left: 10px;
}
.pl11 {
  padding-left: 11px;
}
.pl12 {
  padding-left: 12px;
}
.pl13 {
  padding-left: 13px;
}
.pl14 {
  padding-left: 14px;
}
.pl15 {
  padding-left: 15px;
}
.pl16 {
  padding-left: 16px;
}
.pl17 {
  padding-left: 17px;
}
.pl18 {
  padding-left: 18px;
}
.pl19 {
  padding-left: 19px;
}
.pl20 {
  padding-left: 20px;
}
.pl22 {
  padding-left: 22px;
}
.pl24 {
  padding-left: 24px;
}
.pl26 {
  padding-left: 26px;
}
.pl28 {
  padding-left: 28px;
}
.pl30 {
  padding-left: 30px;
}
.pl32 {
  padding-left: 32px;
}
.pl35 {
  padding-left: 35px;
}
.pl38 {
  padding-left: 38px;
}
.pl40 {
  padding-left: 40px;
}
.pl42 {
  padding-left: 42px;
}
.pl45 {
  padding-left: 45px;
}
.pl48 {
  padding-left: 48px;
}
.pl50 {
  padding-left: 50px;
}
.pl60 {
  padding-left: 60px;
}
.pl70 {
  padding-left: 70px;
}
.pl80 {
  padding-left: 80px;
}
.pl90 {
  padding-left: 90px;
}
.pl100 {
  padding-left: 100px;
}

.pr1 {
  padding-right: 1px;
}
.pr2 {
  padding-right: 2px;
}
.pr3 {
  padding-right: 3px;
}
.pr4 {
  padding-right: 4px;
}
.pr5 {
  padding-right: 5px;
}
.pr6 {
  padding-right: 6px;
}
.pr7 {
  padding-right: 7px;
}
.pr8 {
  padding-right: 8px;
}
.pr9 {
  padding-right: 9px;
}
.pr10 {
  padding-right: 10px;
}
.pr11 {
  padding-right: 11px;
}
.pr12 {
  padding-right: 12px;
}
.pr13 {
  padding-right: 13px;
}
.pr14 {
  padding-right: 14px;
}
.pr15 {
  padding-right: 15px;
}
.pr16 {
  padding-right: 16px;
}
.pr17 {
  padding-right: 17px;
}
.pr18 {
  padding-right: 18px;
}
.pr19 {
  padding-right: 19px;
}
.pr20 {
  padding-right: 20px;
}
.pr22 {
  padding-right: 22px;
}
.pr24 {
  padding-right: 24px;
}
.pr26 {
  padding-right: 26px;
}
.pr28 {
  padding-right: 28px;
}
.pr30 {
  padding-right: 30px;
}
.pr32 {
  padding-right: 32px;
}
.pr35 {
  padding-right: 35px;
}
.pr38 {
  padding-right: 38px;
}
.pr40 {
  padding-right: 40px;
}
.pr42 {
  padding-right: 42px;
}
.pr45 {
  padding-right: 45px;
}
.pr48 {
  padding-right: 48px;
}
.pr50 {
  padding-right: 50px;
}
.pr60 {
  padding-right: 60px;
}
.pr70 {
  padding-right: 70px;
}
.pr80 {
  padding-right: 80px;
}
.pr90 {
  padding-right: 90px;
}
.pr100 {
  padding-right: 100px;
}

.z1 {
  z-index: 1;
}
.z2 {
  z-index: 2;
}
.z3 {
  z-index: 3;
}
.z4 {
  z-index: 4;
}
.z5 {
  z-index: 5;
}
.z6 {
  z-index: 6;
}
.z7 {
  z-index: 7;
}
.z8 {
  z-index: 8;
}
.z9 {
  z-index: 9;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-auto {
  z-index: auto;
}

/* 定位 */

.tl {
  text-align: left;
}
.tc {
  text-align: center;
}
.tr {
  text-align: right;
}

.bc {
  margin: 0 auto;
}
.fl {
  float: left;
  display: inline;
}
.fr {
  float: right;
  display: inline;
}

.vm {
  vertical-align: middle;
}

.zoom {
  zoom: 1;
}

// lin-height
.lh10 { line-height: 10px; }
.lh11 { line-height: 11px; }
.lh12 { line-height: 12px; }
.lh13 { line-height: 13px; }
.lh14 { line-height: 14px; }
.lh15 { line-height: 15px; }
.lh16 { line-height: 16px; }
.lh17 { line-height: 17px; }
.lh18 { line-height: 18px; }
.lh19 { line-height: 19px; }
.lh20 { line-height: 20px; }
.lh21 { line-height: 21px; }
.lh22 { line-height: 22px; }
.lh23 { line-height: 23px; }
.lh24 { line-height: 24px; }
.lh25 { line-height: 25px; }
.lh26 { line-height: 26px; }
.lh27 { line-height: 27px; }
.lh28 { line-height: 28px; }
.lh29 { line-height: 29px; }
.lh30 { line-height: 30px; }
.lh31 { line-height: 31px; }
.lh32 { line-height: 32px; }
.lh33 { line-height: 33px; }
.lh34 { line-height: 34px; }
.lh35 { line-height: 35px; }
.lh36 { line-height: 36px; }
.lh37 { line-height: 37px; }
.lh38 { line-height: 38px; }
.lh39 { line-height: 39px; }
.lh40 { line-height: 40px; }

/* 文字排版 */

.f0 { font-size: 0px; }
.f10 { font-size: 10px; }
.f11 { font-size: 11px; }
.f12 { font-size: 12px; }
.f13 { font-size: 13px; }
.f14 { font-size: 14px; }
.f15 { font-size: 15px; }
.f16 { font-size: 16px; }
.f17 { font-size: 17px; }
.f18 { font-size: 18px; }
.f19 { font-size: 19px; }
.f20 { font-size: 20px; }
.f21 { font-size: 21px; }
.f22 { font-size: 22px; }
.f23 { font-size: 23px; }
.f24 { font-size: 24px; }
.f27 { font-size: 27px; }

.fb { font-weight: bold; }
.fn { font-weight: normal; }

// 长文本折行
.long-text {
  white-space: pre-line;
  word-wrap: break-word;
}

// 单行文字 使用时宽度必须设置
.article-singer-container {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文字
.article-more-container {
  display: -webkit-box;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; //需要显示的行数
  overflow: hidden;
  text-overflow: ellipsis;
}


@media screen and (max-width: 768px) {
  /*长度高度*/
  .w1 {
    width:pw(1);
  }
  .w2 {
    width:pw(2);
  }
  .w3 {
    width:pw(3);
  }
  .w4 {
    width:pw(4);
  }
  .w5 {
    width:pw(5);
  }
  .w6 {
    width:pw(6);
  }
  .w7 {
    width:pw(7);
  }
  .w8 {
    width:pw(8);
  }
  .w9 {
    width:pw(9);
  }
  .w10 {
    width: pw(10);
  }
  .w15 {
    width: pw(15);
  }
  .w20 {
    width: pw(20);
  }
  .w30 {
    width: pw(30);
  }
  .w40 {
    width: pw(40);
  }
  .w50 {
    width: pw(50);
  }
  .w60 {
    width: pw(60);
  }
  .w70 {
    width: pw(70);
  }
  .w80 {
    width: pw(80);
  }
  .w90 {
    width: pw(90);
  }
  .w100 {
    width: pw(100);
  }
  .w110 {
    width: pw(110);
  }
  .w120 {
    width: pw(120);
  }
  .w130 {
    width: pw(130);
  }
  .w140 {
    width: pw(140);
  }
  .w150 {
    width: pw(150);
  }
  .w160 {
    width: pw(160);
  }
  .w170 {
    width: pw(170);
  }
  .w180 {
    width: pw(180);
  }
  .w190 {
    width: pw(190);
  }
  .w200 {
    width: pw(200);
  }
  .w210 {
    width: pw(210);
  }
  .w220 {
    width: pw(220);
  }
  .w230 {
    width: pw(230);
  }
  .w240 {
    width: pw(240);
  }
  .w250 {
    width: pw(250);
  }
  .w260 {
    width: pw(260);
  }
  .w270 {
    width: pw(270);
  }
  .w280 {
    width: pw(280);
  }
  .w290 {
    width: pw(290);
  }
  .w300 {
    width: pw(300);
  }
  .w310 {
    width: pw(310);
  }
  .w320 {
    width: pw(320);
  }
  .w330 {
    width: pw(330);
  }
  .w340 {
    width: pw(340);
  }
  .w350 {
    width: pw(350);
  }
  .w360 {
    width: pw(360);
  }
  .w370 {
    width: pw(370);
  }
  .w380 {
    width: pw(380);
  }
  .w390 {
    width: pw(390);
  }
  .w400 {
    width: pw(400);
  }
  .w410 {
    width: pw(410);
  }
  .w420 {
    width: pw(420);
  }
  .w430 {
    width: pw(430);
  }
  .w440 {
    width: pw(440);
  }
  .w450 {
    width: pw(450);
  }
  .w460 {
    width: pw(460);
  }
  .w470 {
    width: pw(470);
  }
  .w480 {
    width: pw(480);
  }
  .w490 {
    width: pw(490);
  }
  .w500 {
    width: pw(500);
  }
  .w510 {
    width: pw(510);
  }
  .w520 {
    width: pw(520);
  }
  .w530 {
    width: pw(530);
  }
  .w540 {
    width: pw(540);
  }
  .w550 {
    width: pw(550);
  }
  .w560 {
    width: pw(560);
  }
  .w570 {
    width: pw(570);
  }
  .w580 {
    width: pw(580);
  }
  .w590 {
    width: pw(590);
  }
  .w600 {
    width: pw(600);
  }
  .w610 {
    width: pw(610);
  }
  .w620 {
    width: pw(620);
  }
  .w630 {
    width: pw(630);
  }
  .w640 {
    width: pw(640);
  }
  .w650 {
    width: pw(650);
  }
  .w660 {
    width: pw(660);
  }
  .w670 {
    width: pw(670);
  }
  .w680 {
    width: pw(680);
  }
  .w690 {
    width: pw(690);
  }
  .w700 {
    width: pw(700);
  }
  .w710 {
    width: pw(710);
  }
  .w720 {
    width: pw(720);
  }
  .w730 {
    width: pw(730);
  }
  .w740 {
    width: pw(740);
  }
  .w750 {
    width: pw(750);
  }
  .w760 {
    width: pw(760);
  }
  .w770 {
    width: pw(770);
  }
  .w780 {
    width: pw(780);
  }
  .w790 {
    width: pw(790);
  }
  .w800 {
    width: pw(800);
  }
  .w810 {
    width: pw(810);
  }
  .w820 {
    width: pw(820);
  }
  .w830 {
    width: pw(830);
  }
  .w840 {
    width: pw(840);
  }
  .w850 {
    width: pw(850);
  }
  .w860 {
    width: pw(860);
  }
  .w870 {
    width: pw(870);
  }
  .w880 {
    width: pw(880);
  }
  .w890 {
    width: pw(890);
  }
  .w900 {
    width: pw(900);
  }
  .w910 {
    width: pw(910);
  }
  .w920 {
    width: pw(920);
  }
  .w930 {
    width: pw(930);
  }
  .w940 {
    width: pw(940);
  }
  .w950 {
    width: pw(950);
  }
  .w960 {
    width: pw(960);
  }
  .w970 {
    width: pw(970);
  }
  .w980 {
    width: pw(980);
  }
  .w990 {
    width: pw(990);
  }
  .w1000 {
    width: pw(1000);
  }

  .h0 {
    height:pw(0);
  }
  .h1 {
    height:pw(1);
  }
  .h2 {
    height:pw(2);
  }
  .h3 {
    height:pw(3);
  }
  .h4 {
    height:pw(4);
  }
  .h5 {
    height:pw(5);
  }
  .h6 {
    height:pw(6);
  }
  .h7 {
    height:pw(7);
  }
  .h8 {
    height:pw(8);
  }
  .h9 {
    height:pw(9);
  }
  .h10 {
    height: pw(10);
  }
  .h12 {
    height: pw(12);
  }
  .h14 {
    height: pw(14);
  }
  .h16 {
    height: pw(16);
  }
  .h18 {
    height: pw(18);
  }
  .h20 {
    height: pw(20);
  }
  .h22 {
    height: pw(22);
  }
  .h24 {
    height: pw(25);
  }
  .h26 {
    height: pw(26);
  }
  .h28 {
    height: pw(28);
  }
  .h30 {
    height: pw(30);
  }
  .h32 {
    height: pw(32);
  }
  .h34 {
    height: pw(34);
  }
  .h36 {
    height: pw(36);
  }
  .h38 {
    height: pw(38);
  }
  .h40 {
    height: pw(40);
  }
  .h42 {
    height: pw(42);
  }
  .h44 {
    height: pw(44);
  }
  .h46 {
    height: pw(46);
  }
  .h48 {
    height: pw(48);
  }
  .h50 {
    height: pw(50);
  }
  .h52 {
    height: pw(52);
  }
  .h54 {
    height: pw(54);
  }
  .h56 {
    height: pw(56);
  }
  .h58 {
    height: pw(58);
  }
  .h60 {
    height: pw(60);
  }
  .h70 {
    height: pw(70);
  }
  .h80 {
    height: pw(80);
  }
  .h90 {
    height: pw(90);
  }
  .h100 {
    height: pw(100);
  }
  .h120 {
    height: pw(120);
  }
  .h140 {
    height: pw(140);
  }
  .h160 {
    height: pw(160);
  }
  .h180 {
    height: pw(180);
  }
  .h200 {
    height: pw(200);
  }
  .h240 {
    height: pw(340);
  }
  .h300 {
    height: pw(300);
  }
  .h400 {
    height: pw(400);
  }

  /*边距*/
  .m1 {
    margin:pw(1);
  }
  .m2 {
    margin:pw(2);
  }
  .m3 {
    margin:pw(3);
  }
  .m4 {
    margin:pw(4);
  }
  .m5 {
    margin:pw(5);
  }
  .m6 {
    margin:pw(6);
  }
  .m7 {
    margin:pw(7);
  }
  .m8 {
    margin:pw(8);
  }
  .m9 {
    margin:pw(9);
  }
  .m10 {
    margin: pw(10);
  }
  .m11 {
    margin: pw(11);
  }
  .m12 {
    margin: pw(12);
  }
  .m13 {
    margin: pw(13);
  }
  .m14 {
    margin: pw(14);
  }
  .m15 {
    margin: pw(15);
  }
  .m16 {
    margin: pw(16);
  }
  .m17 {
    margin: pw(17);
  }
  .m18 {
    margin: pw(18);
  }
  .m19 {
    margin: pw(19);
  }
  .m20 {
    margin: pw(20);
  }
  .m22 {
    margin: pw(22);
  }
  .m24 {
    margin: pw(23);
  }
  .m26 {
    margin: pw(26);
  }
  .m28 {
    margin: pw(28);
  }
  .m30 {
    margin: pw(30);
  }
  .m35 {
    margin: pw(35);
  }
  .m40 {
    margin: pw(40);
  }
  .m50 {
    margin: pw(40);
  }
  .m60 {
    margin: pw(60);
  }

  .mt1 {
    margin-top:pw(1);
  }
  .mt2 {
    margin-top:pw(2);
  }
  .mt3 {
    margin-top:pw(3);
  }
  .mt4 {
    margin-top:pw(4);
  }
  .mt5 {
    margin-top:pw(5);
  }
  .mt6 {
    margin-top:pw(6);
  }
  .mt7 {
    margin-top:pw(7);
  }
  .mt8 {
    margin-top:pw(8);
  }
  .mt9 {
    margin-top:pw(9);
  }
  .mt10 {
    margin-top: pw(10);
  }
  .mt11 {
    margin-top: pw(11);
  }
  .mt12 {
    margin-top: pw(12);
  }
  .mt13 {
    margin-top: pw(13);
  }
  .mt14 {
    margin-top: pw(14);
  }
  .mt15 {
    margin-top: pw(15);
  }
  .mt16 {
    margin-top: pw(16);
  }
  .mt17 {
    margin-top: pw(17);
  }
  .mt18 {
    margin-top: pw(18);
  }
  .mt19 {
    margin-top: pw(19);
  }
  .mt20 {
    margin-top: pw(20);
  }
  .mt22 {
    margin-top: pw(22);
  }
  .mt24 {
    margin-top: pw(24);
  }
  .mt26 {
    margin-top: pw(26);
  }
  .mt28 {
    margin-top: pw(28);
  }
  .mt30 {
    margin-top: pw(30);
  }
  .mt32 {
    margin-top: pw(32);
  }
  .mt35 {
    margin-top: pw(35);
  }
  .mt38 {
    margin-top: pw(38);
  }
  .mt40 {
    margin-top: pw(40);
  }
  .mt42 {
    margin-top: pw(42);
  }
  .mt45 {
    margin-top: pw(45);
  }
  .mt48 {
    margin-top: pw(48);
  }
  .mt50 {
    margin-top: pw(50);
  }
  .mt60 {
    margin-top: pw(60);
  }
  .mt70 {
    margin-top: pw(70);
  }
  .mt80 {
    margin-top: pw(80);
  }
  .mt90 {
    margin-top: pw(90);
  }
  .mt100 {
    margin-top: pw(100);
  }

  .mb1 {
    margin-bottom:pw(1);
  }
  .mb2 {
    margin-bottom:pw(2);
  }
  .mb3 {
    margin-bottom:pw(3);
  }
  .mb4 {
    margin-bottom:pw(4);
  }
  .mb5 {
    margin-bottom:pw(5);
  }
  .mb6 {
    margin-bottom:pw(6);
  }
  .mb7 {
    margin-bottom:pw(7);
  }
  .mb8 {
    margin-bottom:pw(8);
  }
  .mb9 {
    margin-bottom:pw(9);
  }
  .mb10 {
    margin-bottom: pw(10);
  }
  .mb11 {
    margin-bottom: pw(11);
  }
  .mb12 {
    margin-bottom: pw(12);
  }
  .mb13 {
    margin-bottom: pw(13);
  }
  .mb14 {
    margin-bottom: pw(14);
  }
  .mb15 {
    margin-bottom: pw(15);
  }
  .mb16 {
    margin-bottom: pw(16);
  }
  .mb17 {
    margin-bottom: pw(17);
  }
  .mb18 {
    margin-bottom: pw(18);
  }
  .mb19 {
    margin-bottom: pw(19);
  }
  .mb20 {
    margin-bottom: pw(20);
  }
  .mb22 {
    margin-bottom: pw(22);
  }
  .mb24 {
    margin-bottom: pw(24);
  }
  .mb26 {
    margin-bottom: pw(26);
  }
  .mb28 {
    margin-bottom: pw(28);
  }
  .mb30 {
    margin-bottom: pw(30);
  }
  .mb32 {
    margin-bottom: pw(32);
  }
  .mb35 {
    margin-bottom: pw(35);
  }
  .mb38 {
    margin-bottom: pw(38);
  }
  .mb40 {
    margin-bottom: pw(40);
  }
  .mb42 {
    margin-bottom: pw(42);
  }
  .mb45 {
    margin-bottom: pw(45);
  }
  .mb48 {
    margin-bottom: pw(48);
  }
  .mb50 {
    margin-bottom: pw(50);
  }
  .mb60 {
    margin-bottom: pw(60);
  }
  .mb70 {
    margin-bottom: pw(70);
  }
  .mb80 {
    margin-bottom: pw(80);
  }
  .mb90 {
    margin-bottom: pw(90);
  }
  .mb100 {
    margin-bottom: pw(100);
  }

  .ml1 {
    margin-left:pw(1);
  }
  .ml2 {
    margin-left:pw(2);
  }
  .ml3 {
    margin-left:pw(3);
  }
  .ml4 {
    margin-left:pw(4);
  }
  .ml5 {
    margin-left:pw(5);
  }
  .ml6 {
    margin-left:pw(6);
  }
  .ml7 {
    margin-left:pw(7);
  }
  .ml8 {
    margin-left:pw(8);
  }
  .ml9 {
    margin-left:pw(9);
  }
  .ml10 {
    margin-left: pw(10);
  }
  .ml11 {
    margin-left: pw(11);
  }
  .ml12 {
    margin-left: pw(12);
  }
  .ml13 {
    margin-left: pw(13);
  }
  .ml14 {
    margin-left: pw(14);
  }
  .ml15 {
    margin-left: pw(15);
  }
  .ml16 {
    margin-left: pw(16);
  }
  .ml17 {
    margin-left: pw(17);
  }
  .ml18 {
    margin-left: pw(18);
  }
  .ml19 {
    margin-left: pw(19);
  }
  .ml20 {
    margin-left: pw(20);
  }
  .ml22 {
    margin-left: pw(22);
  }
  .ml24 {
    margin-left: pw(24);
  }
  .ml26 {
    margin-left: pw(26);
  }
  .ml28 {
    margin-left: pw(28);
  }
  .ml30 {
    margin-left: pw(30);
  }
  .ml32 {
    margin-left: pw(32);
  }
  .ml35 {
    margin-left: pw(35);
  }
  .ml38 {
    margin-left: pw(38);
  }
  .ml40 {
    margin-left: pw(40);
  }
  .ml42 {
    margin-left: pw(42);
  }
  .ml45 {
    margin-left: pw(45);
  }
  .ml48 {
    margin-left: pw(48);
  }
  .ml50 {
    margin-left: pw(50);
  }
  .ml60 {
    margin-left: pw(60);
  }
  .ml70 {
    margin-left: pw(70);
  }
  .ml80 {
    margin-left: pw(80);
  }
  .ml90 {
    margin-left: pw(90);
  }
  .ml100 {
    margin-left: pw(100);
  }

  .mr1 {
    margin-right:pw(1);
  }
  .mr2 {
    margin-right:pw(2);
  }
  .mr3 {
    margin-right:pw(3);
  }
  .mr4 {
    margin-right:pw(4);
  }
  .mr5 {
    margin-right:pw(5);
  }
  .mr6 {
    margin-right:pw(6);
  }
  .mr7 {
    margin-right:pw(7);
  }
  .mr8 {
    margin-right:pw(8);
  }
  .mr9 {
    margin-right:pw(9);
  }
  .mr10 {
    margin-right: pw(10);
  }
  .mr11 {
    margin-right: pw(11);
  }
  .mr12 {
    margin-right: pw(12);
  }
  .mr13 {
    margin-right: pw(13);
  }
  .mr14 {
    margin-right: pw(14);
  }
  .mr15 {
    margin-right: pw(15);
  }
  .mr16 {
    margin-right: pw(16);
  }
  .mr17 {
    margin-right: pw(17);
  }
  .mr18 {
    margin-right: pw(18);
  }
  .mr19 {
    margin-right: pw(19);
  }
  .mr20 {
    margin-right: pw(20);
  }
  .mr22 {
    margin-right: pw(22);
  }
  .mr24 {
    margin-right: pw(24);
  }
  .mr26 {
    margin-right: pw(26);
  }
  .mr28 {
    margin-right: pw(28);
  }
  .mr30 {
    margin-right: pw(30);
  }
  .mr32 {
    margin-right: pw(32);
  }
  .mr35 {
    margin-right: pw(35);
  }
  .mr38 {
    margin-right: pw(38);
  }
  .mr40 {
    margin-right: pw(40);
  }
  .mr42 {
    margin-right: pw(42);
  }
  .mr45 {
    margin-right: pw(45);
  }
  .mr48 {
    margin-right: pw(48);
  }
  .mr50 {
    margin-right: pw(50);
  }
  .mr60 {
    margin-right: pw(60);
  }
  .mr70 {
    margin-right: pw(70);
  }
  .mr80 {
    margin-right: pw(80);
  }
  .mr90 {
    margin-right: pw(90);
  }
  .mr100 {
    margin-right: pw(100);
  }

  .p1 {
    padding:pw( 1);
  }
  .p2 {
    padding:pw( 2);
  }
  .p3 {
    padding:pw( 3);
  }
  .p4 {
    padding:pw( 4);
  }
  .p5 {
    padding:pw( 5);
  }
  .p6 {
    padding:pw( 6);
  }
  .p7 {
    padding:pw( 7);
  }
  .p8 {
    padding:pw( 8);
  }
  .p9 {
    padding:pw( 9);
  }
  .p10 {
    padding: pw(10);
  }
  .p11 {
    padding: pw(11);
  }
  .p12 {
    padding: pw(12);
  }
  .p13 {
    padding: pw(13);
  }
  .p14 {
    padding: pw(14);
  }
  .p15 {
    padding: pw(15);
  }
  .p16 {
    padding: pw(16);
  }
  .p17 {
    padding: pw(17);
  }
  .p18 {
    padding: pw(18);
  }
  .p19 {
    padding: pw(19);
  }
  .p20 {
    padding: pw(20);
  }
  .p22 {
    padding: pw(22);
  }
  .p24 {
    padding: pw(23);
  }
  .p26 {
    padding: pw(26);
  }
  .p28 {
    padding: pw(28);
  }
  .p30 {
    padding: pw(30);
  }
  .p35 {
    padding: pw(35);
  }
  .p40 {
    padding: pw(40);
  }
  .p50 {
    padding: pw(40);
  }
  .p60 {
    padding: pw(60);
  }

  .ptsafe {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }

  .pt1 {
    padding-top:pw( 1);
  }
  .pt2 {
    padding-top:pw( 2);
  }
  .pt3 {
    padding-top:pw( 3);
  }
  .pt4 {
    padding-top:pw( 4);
  }
  .pt5 {
    padding-top:pw( 5);
  }
  .pt6 {
    padding-top:pw( 6);
  }
  .pt7 {
    padding-top:pw( 7);
  }
  .pt8 {
    padding-top:pw( 8);
  }
  .pt9 {
    padding-top:pw( 9);
  }
  .pt10 {
    padding-top: pw(10);
  }
  .pt11 {
    padding-top: pw(11);
  }
  .pt12 {
    padding-top: pw(12);
  }
  .pt13 {
    padding-top: pw(13);
  }
  .pt14 {
    padding-top: pw(14);
  }
  .pt15 {
    padding-top: pw(15);
  }
  .pt16 {
    padding-top: pw(16);
  }
  .pt17 {
    padding-top: pw(17);
  }
  .pt18 {
    padding-top: pw(18);
  }
  .pt19 {
    padding-top: pw(19);
  }
  .pt20 {
    padding-top: pw(20);
  }
  .pt22 {
    padding-top: pw(22);
  }
  .pt24 {
    padding-top: pw(24);
  }
  .pt26 {
    padding-top: pw(26);
  }
  .pt28 {
    padding-top: pw(28);
  }
  .pt30 {
    padding-top: pw(30);
  }
  .pt32 {
    padding-top: pw(32);
  }
  .pt35 {
    padding-top: pw(35);
  }
  .pt38 {
    padding-top: pw(38);
  }
  .pt40 {
    padding-top: pw(40);
  }
  .pt42 {
    padding-top: pw(42);
  }
  .pt45 {
    padding-top: pw(45);
  }
  .pt48 {
    padding-top: pw(48);
  }
  .pt50 {
    padding-top: pw(50);
  }
  .pt60 {
    padding-top: pw(60);
  }
  .pt70 {
    padding-top: pw(70);
  }
  .pt80 {
    padding-top: pw(80);
  }
  .pt90 {
    padding-top: pw(90);
  }
  .pt100 {
    padding-top: pw(100);
  }

  .pbsafe {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
  .pb1 {
    padding-bottom:pw( 1);
  }
  .pb2 {
    padding-bottom:pw( 2);
  }
  .pb3 {
    padding-bottom:pw( 3);
  }
  .pb4 {
    padding-bottom:pw( 4);
  }
  .pb5 {
    padding-bottom:pw( 5);
  }
  .pb6 {
    padding-bottom:pw( 6);
  }
  .pb7 {
    padding-bottom:pw( 7);
  }
  .pb8 {
    padding-bottom:pw( 8);
  }
  .pb9 {
    padding-bottom:pw( 9);
  }
  .pb10 {
    padding-bottom: pw(10);
  }
  .pb11 {
    padding-bottom: pw(11);
  }
  .pb12 {
    padding-bottom: pw(12);
  }
  .pb13 {
    padding-bottom: pw(13);
  }
  .pb14 {
    padding-bottom: pw(14);
  }
  .pb15 {
    padding-bottom: pw(15);
  }
  .pb16 {
    padding-bottom: pw(16);
  }
  .pb17 {
    padding-bottom: pw(17);
  }
  .pb18 {
    padding-bottom: pw(18);
  }
  .pb19 {
    padding-bottom: pw(19);
  }
  .pb20 {
    padding-bottom: pw(20);
  }
  .pb22 {
    padding-bottom: pw(22);
  }
  .pb24 {
    padding-bottom: pw(24);
  }
  .pb26 {
    padding-bottom: pw(26);
  }
  .pb28 {
    padding-bottom: pw(28);
  }
  .pb30 {
    padding-bottom: pw(30);
  }
  .pb32 {
    padding-bottom: pw(32);
  }
  .pb35 {
    padding-bottom: pw(35);
  }
  .pb38 {
    padding-bottom: pw(38);
  }
  .pb40 {
    padding-bottom: pw(40);
  }
  .pb42 {
    padding-bottom: pw(42);
  }
  .pb45 {
    padding-bottom: pw(45);
  }
  .pb48 {
    padding-bottom: pw(48);
  }
  .pb50 {
    padding-bottom: pw(50);
  }
  .pb60 {
    padding-bottom: pw(60);
  }
  .pb70 {
    padding-bottom: pw(70);
  }
  .pb80 {
    padding-bottom: pw(80);
  }
  .pb90 {
    padding-bottom: pw(90);
  }
  .pb100 {
    padding-bottom: pw(100);
  }

  .pl1 {
    padding-left:pw( 1);
  }
  .pl2 {
    padding-left:pw( 2);
  }
  .pl3 {
    padding-left:pw( 3);
  }
  .pl4 {
    padding-left:pw( 4);
  }
  .pl5 {
    padding-left:pw( 5);
  }
  .pl6 {
    padding-left:pw( 6);
  }
  .pl7 {
    padding-left:pw( 7);
  }
  .pl8 {
    padding-left:pw( 8);
  }
  .pl9 {
    padding-left:pw( 9);
  }
  .pl10 {
    padding-left: pw(10);
  }
  .pl11 {
    padding-left: pw(11);
  }
  .pl12 {
    padding-left: pw(12);
  }
  .pl13 {
    padding-left: pw(13);
  }
  .pl14 {
    padding-left: pw(14);
  }
  .pl15 {
    padding-left: pw(15);
  }
  .pl16 {
    padding-left: pw(16);
  }
  .pl17 {
    padding-left: pw(17);
  }
  .pl18 {
    padding-left: pw(18);
  }
  .pl19 {
    padding-left: pw(19);
  }
  .pl20 {
    padding-left: pw(20);
  }
  .pl22 {
    padding-left: pw(22);
  }
  .pl24 {
    padding-left: pw(24);
  }
  .pl26 {
    padding-left: pw(26);
  }
  .pl28 {
    padding-left: pw(28);
  }
  .pl30 {
    padding-left: pw(30);
  }
  .pl32 {
    padding-left: pw(32);
  }
  .pl35 {
    padding-left: pw(35);
  }
  .pl38 {
    padding-left: pw(38);
  }
  .pl40 {
    padding-left: pw(40);
  }
  .pl42 {
    padding-left: pw(42);
  }
  .pl45 {
    padding-left: pw(45);
  }
  .pl48 {
    padding-left: pw(48);
  }
  .pl50 {
    padding-left: pw(50);
  }
  .pl60 {
    padding-left: pw(60);
  }
  .pl70 {
    padding-left: pw(70);
  }
  .pl80 {
    padding-left: pw(80);
  }
  .pl90 {
    padding-left: pw(90);
  }
  .pl100 {
    padding-left: pw(100);
  }

  .pr1 {
    padding-right:pw( 1);
  }
  .pr2 {
    padding-right:pw( 2);
  }
  .pr3 {
    padding-right:pw( 3);
  }
  .pr4 {
    padding-right:pw( 4);
  }
  .pr5 {
    padding-right:pw( 5);
  }
  .pr6 {
    padding-right:pw( 6);
  }
  .pr7 {
    padding-right:pw( 7);
  }
  .pr8 {
    padding-right:pw( 8);
  }
  .pr9 {
    padding-right:pw( 9);
  }
  .pr10 {
    padding-right: pw(10);
  }
  .pr11 {
    padding-right: pw(11);
  }
  .pr12 {
    padding-right: pw(12);
  }
  .pr13 {
    padding-right: pw(13);
  }
  .pr14 {
    padding-right: pw(14);
  }
  .pr15 {
    padding-right: pw(15);
  }
  .pr16 {
    padding-right: pw(16);
  }
  .pr17 {
    padding-right: pw(17);
  }
  .pr18 {
    padding-right: pw(18);
  }
  .pr19 {
    padding-right: pw(19);
  }
  .pr20 {
    padding-right: pw(20);
  }
  .pr22 {
    padding-right: pw(22);
  }
  .pr24 {
    padding-right: pw(24);
  }
  .pr26 {
    padding-right: pw(26);
  }
  .pr28 {
    padding-right: pw(28);
  }
  .pr30 {
    padding-right: pw(30);
  }
  .pr32 {
    padding-right: pw(32);
  }
  .pr35 {
    padding-right: pw(35);
  }
  .pr38 {
    padding-right: pw(38);
  }
  .pr40 {
    padding-right: pw(40);
  }
  .pr42 {
    padding-right: pw(42);
  }
  .pr45 {
    padding-right: pw(45);
  }
  .pr48 {
    padding-right: pw(48);
  }
  .pr50 {
    padding-right: pw(50);
  }
  .pr60 {
    padding-right: pw(60);
  }
  .pr70 {
    padding-right: pw(70);
  }
  .pr80 {
    padding-right: pw(80);
  }
  .pr90 {
    padding-right: pw(90);
  }
  .pr100 {
    padding-right: pw(100);
  }

  // lin-height
  .lh10 { line-height: pw(10); }
  .lh11 { line-height: pw(11); }
  .lh12 { line-height: pw(12); }
  .lh13 { line-height: pw(13); }
  .lh14 { line-height: pw(14); }
  .lh15 { line-height: pw(15); }
  .lh16 { line-height: pw(16); }
  .lh17 { line-height: pw(17); }
  .lh18 { line-height: pw(18); }
  .lh19 { line-height: pw(19); }
  .lh20 { line-height: pw(20); }
  .lh21 { line-height: pw(21); }
  .lh22 { line-height: pw(22); }
  .lh23 { line-height: pw(23); }
  .lh24 { line-height: pw(24); }
  .lh25 { line-height: pw(25); }
  .lh26 { line-height: pw(26); }
  .lh27 { line-height: pw(27); }
  .lh28 { line-height: pw(28); }
  .lh29 { line-height: pw(29); }
  .lh30 { line-height: pw(30); }
  .lh31 { line-height: pw(31); }
  .lh32 { line-height: pw(32); }
  .lh33 { line-height: pw(33); }
  .lh34 { line-height: pw(34); }
  .lh35 { line-height: pw(35); }
  .lh36 { line-height: pw(36); }
  .lh37 { line-height: pw(37); }
  .lh38 { line-height: pw(38); }
  .lh39 { line-height: pw(39); }
  .lh40 { line-height: pw(40); }

  /* 文字排版 */

  .f0 { font-size:pw( 0); }
  .f10 { font-size: pw(10); }
  .f11 { font-size: pw(11); }
  .f12 { font-size: pw(12); }
  .f13 { font-size: pw(13); }
  .f14 { font-size: pw(14); }
  .f15 { font-size: pw(15); }
  .f16 { font-size: pw(16); }
  .f17 { font-size: pw(17); }
  .f18 { font-size: pw(18); }
  .f19 { font-size: pw(19); }
  .f20 { font-size: pw(20); }
  .f21 { font-size: pw(21); }
  .f22 { font-size: pw(22); }
  .f23 { font-size: pw(23); }
  .f24 { font-size: pw(24); }
  .f27 { font-size: pw(27); }

}