@import './mixin.scss';

//默认设计稿的宽度
$designWidth: 1440;
//默认设计稿的高度
$designHeight: 810;

//默认设计稿的宽度
$designWidthH5: 430;

//px转为vw的函数
@function vw($px) {
  @return $px * 100vw / #{$designWidth};
}

//px转为vh的函数
@function vh($px) {
  @return $px * 100vh / #{$designHeight};
}

//H5中px转为vw的函数
@function pw($px) {
  @return $px * 100vw / #{$designWidthH5};
}

$navTopPadding: 80px;
$bannerNavHeight: 56px;
$MbannerNavHeight: pw(40);

@media screen and (min-width: 769px) {
  // 通用首页banner样式
  .bg-color-box {
    width: 100%;
    height: 620px;
    padding-top: $navTopPadding;
    padding-bottom: 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .banner-title-box {
      width: 1000px;
      height: calc(100% - #{$bannerNavHeight});
      display: flex;
      justify-content: space-between;
      align-items: center;

      .banner-title {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .banner-title-weight {
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: 50px;
          margin-bottom: 16px;
          color: #041128;
        }
        .banner-title-item {
          font-family: 'PingFang SC';
          font-weight: 300;
          font-size: 28px;
          color: #333333;
        }
      }
    }

    .banner-nav {
      height: $bannerNavHeight;
      width: 1000px;
      display: flex;
      justify-content: center;
      align-items: center;

      .item-box {
        width: 175px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .item-title {
          color: #333333;
          font-weight: 500;
          font-size: 23px;
          margin-bottom: 3px;
        }
        .item-desc {
          color: #4F4F4F;
          font-size: 15px;
        }
      }
      .item-line {
        width: 1px;
        height: 60%;
        background: rgba($color: #000000, $alpha: 0.3);
      }
    }
  }
}
@media screen and (max-width: 768px) {
  // 通用首页banner样式
  .bg-color-box {
    width: 100%;
    height: pw(390);
    padding-bottom: pw(20);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .banner-title-box {
      width: 100%;
      height: calc(100% - #{$MbannerNavHeight});
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .banner-title {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: pw(30);

        .banner-title-weight {
          font-family: 'PingFang SC';
          font-weight: 600;
          font-size: pw(24);
          margin-bottom: pw(16);
          color: #000000;
        }
        .banner-title-item {
          font-family: 'PingFang SC';
          font-weight: 300;
          font-size: pw(16);
          color: #000000;
        }
      }
    }

    .banner-nav {
      height: $MbannerNavHeight;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .item-box {
        width: pw(109);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .item-title {
          color: #333333;
          font-weight: 500;
          font-size: pw(16);
          margin-bottom: pw(3);
        }
        .item-desc {
          color: #4F4F4F;
          font-size: pw(10);
        }
      }
      .item-line {
        width: 1px;
        height: 60%;
        background: rgba($color: #000000, $alpha: 0.3);
      }
    }
  }
}

.vjs-poster {
  background-size: cover !important;
}