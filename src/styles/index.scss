@import './utils.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;

}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

.pointer {
  cursor: pointer;
}

/* 底边框 */
.b-border {
  position: relative;
}
.b-border:before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  // background: #d9d9d9;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
/* 上边框 */
.t-border {
  position: relative;
}
.t-border:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  // background: #d9d9d9;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
/* 右边框 */
.r-border {
  position: relative;
}
.r-border:before {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  // background: #d9d9d9;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
/* 左边框 */
.l-border {
  position: relative;
}
.l-border:before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  // background: #d9d9d9;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}

/* 四条边 */
.setBorderAll {
  position: relative;
  &:after {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(0.5);
    transform-origin: left top;
    box-sizing: border-box;
    // border: 1px solid #e5e5e5;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
  }
}

*|*:fullscreen:not(:root) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  min-width: 0 !important;
  max-width: none !important;
  min-height: 0 !important;
  max-height: none !important;
  box-sizing: border-box !important;
  object-fit: contain;
  transform: none !important;
}

p {
  margin: 0;
}

.animate__delay-100 {
  animation-delay: 0.1s;
}

.animate__delay-200 {
  animation-delay: 0.2s;
}

.animate__delay-300 {
  animation-delay: 0.3s;
}

.animate__delay-400 {
  animation-delay: 0.4s;
}

.animate__delay-500 {
  animation-delay: 0.5s;
}

.animate__delay-600 {
  animation-delay: 0.6s;
}

.animate__delay-700 {
  animation-delay: 0.7s;
}

.animate__delay-800 {
  animation-delay: 0.8s;
}

.animate__delay-900 {
  animation-delay: 0.9s;
}

.animate__delay-1000 {
  animation-delay: 1s;
}

.animate__delay-1200 {
  animation-delay: 1.2s;
}

.animate__delay-1300 {
  animation-delay: 1.3s;
}

.animate__delay-1400 {
  animation-delay: 1.4s;
}

.animate__delay-1600 {
  animation-delay: 1.6s;
}

.animate__delay-1700 {
  animation-delay: 1.7s;
}

.slinter {
  .swiper-wrapper {
    transition-timing-function: linear !important;
  }
}