<svg width="341" height="328" viewBox="0 0 341 328" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.617 262.725C222.617 262.725 70.1748 330.245 17.2205 211.583C-0.844552 155.635 8.98211 110.959 8.98211 110.959C8.98211 110.959 25.4946 48.5861 82.7563 48.4925C168.092 58.3992 89.0599 169.311 148.064 214.67C201.106 238.298 222.617 262.725 222.617 262.725Z" fill="url(#paint0_linear_199_3380)"/>
<mask id="mask0_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="6" y="48" width="217" height="234">
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.617 262.725C222.617 262.725 70.1748 330.245 17.2205 211.583C-0.844552 155.635 8.98211 110.959 8.98211 110.959C8.98211 110.959 25.4946 48.5861 82.7563 48.4925C168.092 58.3992 89.0599 169.311 148.064 214.67C201.106 238.298 222.617 262.725 222.617 262.725Z" fill="white"/>
</mask>
<g mask="url(#mask0_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-65.3768 84.4918C-65.3768 84.4918 3.0676 -10.555 120.122 13.797C112.92 49.622 139.772 103.128 139.772 103.128C139.772 103.128 12.3976 104.008 -16.1278 177.511C-37.7536 146.838 -65.3768 84.4918 -65.3768 84.4918Z" fill="url(#paint1_linear_199_3380)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.681173 191.897C0.681173 191.897 59.6038 122.185 159.165 139.175C152.797 165.33 171.737 160.661 171.737 160.661C171.737 160.661 46.2947 200.445 33.0129 242.333C14.7884 220.119 0.681173 191.897 0.681173 191.897Z" fill="url(#paint2_linear_199_3380)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M71.465 311.505C71.465 311.505 72.1708 234.171 146.471 182.839C156.495 202.158 166.344 187.741 166.344 187.741C166.344 187.741 105.92 288.437 119.958 321.276C95.9954 319.427 71.465 311.505 71.465 311.505Z" fill="url(#paint3_linear_199_3380)"/>
<g opacity="0.446167" filter="url(#filter0_f_199_3380)">
<ellipse cx="161.21" cy="137.667" rx="114.914" ry="107.679" transform="rotate(-93 161.21 137.667)" fill="#563600"/>
</g>
<g opacity="0.299277" filter="url(#filter1_f_199_3380)">
<ellipse cx="-23.5195" cy="282.217" rx="149.099" ry="115.023" transform="rotate(-93 -23.5195 282.217)" fill="#FDCE49"/>
</g>
<g opacity="0.275156" filter="url(#filter2_f_199_3380)">
<ellipse cx="13.849" cy="57.8499" rx="73.3141" ry="57.7177" transform="rotate(-93 13.849 57.8499)" fill="#FDCE49"/>
</g>
</g>
<g filter="url(#filter3_i_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M242.613 226.386C242.613 226.386 281.815 231.535 295.083 208.075C308.35 184.616 285.721 176.974 283.353 178.872C280.984 180.769 244.017 216.397 244.017 216.397L242.613 226.386Z" fill="url(#paint4_linear_199_3380)"/>
</g>
<g filter="url(#filter4_ii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.56 286.954C180.56 286.954 202.999 320.71 223.837 326.007C244.676 331.304 260.309 317.692 242.883 297.608C225.456 277.523 225.905 274.756 225.905 274.756L180.56 286.954Z" fill="url(#paint5_linear_199_3380)"/>
</g>
<g filter="url(#filter5_iii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M233.907 272.362C233.907 272.362 241.424 279.359 256.865 277.027C272.306 274.694 272.567 252.698 262.558 242.06C259.616 240.204 256.865 240.625 256.865 240.625L233.907 272.362Z" fill="url(#paint6_linear_199_3380)"/>
</g>
<g filter="url(#filter6_i_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M175.15 194.243C175.15 194.243 151.289 259.815 180.56 286.954C203.141 286.063 219.486 283.399 233.907 277.61C253.827 269.613 261.092 250.137 260.669 240.625C260.421 235.026 256.529 233.653 251.647 228.242C247.88 224.296 244.332 211.645 244.332 211.645L175.15 194.243Z" fill="url(#paint7_linear_199_3380)"/>
</g>
<mask id="mask1_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="165" y="194" width="96" height="93">
<path fill-rule="evenodd" clip-rule="evenodd" d="M175.15 194.243C175.15 194.243 151.289 259.815 180.56 286.954C203.141 286.063 219.486 283.399 233.907 277.61C253.827 269.613 261.092 250.137 260.669 240.625C260.421 235.026 256.529 233.653 251.647 228.242C247.88 224.296 244.332 211.645 244.332 211.645L175.15 194.243Z" fill="white"/>
</mask>
<g mask="url(#mask1_199_3380)">
<g opacity="0.753339" filter="url(#filter7_f_199_3380)">
<ellipse cx="207.532" cy="218.979" rx="28.0076" ry="24.7362" fill="#FDF2A4"/>
</g>
<g opacity="0.502425" filter="url(#filter8_f_199_3380)">
<ellipse cx="219.888" cy="192.182" rx="44.4827" ry="21.0257" fill="#FF9913"/>
</g>
</g>
<g opacity="0.311393" filter="url(#filter9_f_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M163.809 238.643C163.809 238.643 162.831 234.488 165.122 232.757C167.413 231.026 173.433 229.295 173.433 229.295L182.676 215.62L240.977 215.793L247.971 229.295L257.556 234.142L258.368 237.787L163.809 238.643Z" fill="#FF9313"/>
</g>
<g filter="url(#filter10_iii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.288 217.745L180.366 217.501L180.621 217.502L180.62 217.85C180.621 217.502 180.621 217.502 180.621 217.502L180.622 217.502L180.626 217.502L180.639 217.502L180.694 217.502L180.906 217.503L181.717 217.504C182.418 217.506 183.427 217.507 184.654 217.509C187.107 217.514 190.436 217.519 193.937 217.522C200.94 217.528 208.626 217.528 211.372 217.502L211.679 217.499L211.689 217.571C215.134 217.66 222.529 218.292 229.274 218.924C232.94 219.268 236.422 219.612 238.988 219.87C240.271 219.999 241.324 220.107 242.057 220.182C242.424 220.22 242.71 220.25 242.905 220.27L243.127 220.293L243.184 220.299L243.198 220.3L243.202 220.301L243.202 220.301C243.203 220.301 243.203 220.301 243.167 220.648L243.466 220.47C243.996 221.362 244.951 222.377 246.16 223.459C247.364 224.536 248.798 225.658 250.264 226.767C250.872 227.228 251.488 227.688 252.095 228.141L252.097 228.142C252.945 228.774 253.775 229.393 254.54 229.982C255.855 230.994 257.011 231.938 257.788 232.748C259.882 234.931 262.432 238.113 265.529 242.686L265.549 242.716L265.563 242.749C265.999 243.805 266.455 244.805 266.803 245.543C266.977 245.911 267.124 246.213 267.227 246.423C267.278 246.528 267.319 246.61 267.347 246.665L267.375 246.721C267.387 246.737 267.402 246.759 267.42 246.786C267.459 246.844 267.511 246.927 267.568 247.034C267.682 247.248 267.816 247.557 267.906 247.953C268.086 248.749 268.077 249.871 267.388 251.216L267.365 251.26L267.331 251.296C265.524 253.213 263.604 255.389 261.737 257.915C257.806 264.119 252.266 268.717 247.72 271.762C245.444 273.286 243.412 274.423 241.949 275.181C241.217 275.559 240.627 275.843 240.219 276.032C240.015 276.127 239.856 276.198 239.748 276.245C239.694 276.269 239.653 276.287 239.625 276.299L239.593 276.313L239.584 276.316L239.582 276.317L239.581 276.318C239.581 276.318 239.581 276.318 239.446 275.996L239.581 276.318L239.573 276.321L239.573 276.321L232.058 279.253L232.127 279.409L231.66 279.409L231.416 279.504L231.371 279.409L230.249 279.409L230.309 279.489C230.622 279.898 231.07 280.487 231.607 281.201C232.683 282.631 234.12 284.567 235.57 286.591C237.018 288.615 238.48 290.732 239.604 292.522C240.165 293.417 240.645 294.235 240.996 294.922C241.341 295.595 241.585 296.184 241.637 296.606C241.67 296.878 241.589 297.152 241.459 297.408C241.327 297.668 241.13 297.937 240.888 298.213C240.403 298.763 239.703 299.376 238.87 300.018C237.203 301.304 234.954 302.745 232.706 304.092C230.455 305.44 228.194 306.698 226.497 307.62C225.648 308.08 224.94 308.457 224.443 308.719C224.195 308.849 224 308.951 223.867 309.021L223.714 309.1L223.675 309.12L223.665 309.125L223.663 309.127L223.662 309.127C223.662 309.127 223.662 309.127 223.502 308.817C223.605 309.15 223.605 309.15 223.605 309.15L223.604 309.15L223.602 309.151L223.594 309.154L223.563 309.163C223.536 309.171 223.495 309.184 223.442 309.2C223.336 309.231 223.179 309.278 222.976 309.336C222.57 309.453 221.977 309.619 221.232 309.815C219.741 310.208 217.636 310.721 215.18 311.205C210.275 312.172 203.943 313.029 198.295 312.561L198.263 312.559L198.232 312.55C198.11 312.517 197.984 312.483 197.854 312.448C195.875 311.912 193.07 311.153 191.232 309.046C190.187 307.849 190.207 306.294 190.45 305.107C190.572 304.507 190.755 303.979 190.907 303.602C190.977 303.43 191.04 303.289 191.089 303.185C191.043 303.123 190.986 303.045 190.918 302.952C190.874 302.891 190.825 302.824 190.772 302.751C190.49 302.364 190.087 301.81 189.602 301.138C188.631 299.794 187.328 297.978 186.004 296.092C184.68 294.206 183.332 292.248 182.271 290.62C181.741 289.806 181.28 289.072 180.929 288.469C180.621 287.939 180.384 287.488 180.265 287.168C173.936 282.327 169.739 273.127 166.966 263.764C164.174 254.342 162.802 244.674 162.174 238.912C161.831 235.759 164.147 233.02 167.282 232.68L171.537 232.218C174.142 231.935 176.334 230.143 177.13 227.647L180.288 217.745ZM223.502 308.817L223.605 309.15L223.635 309.141L223.662 309.127L223.502 308.817ZM210.577 218.205C211.831 229.119 216.979 244.304 221.816 256.772C224.256 263.061 226.621 268.669 228.376 272.705C229.254 274.723 229.979 276.348 230.485 277.469C230.725 278.001 230.915 278.419 231.05 278.712L229.542 278.712L228.832 278.712L229.266 279.273L229.542 279.06C229.266 279.273 229.266 279.273 229.266 279.274L229.267 279.274L229.268 279.276L229.274 279.284L229.299 279.316L229.394 279.439C229.477 279.548 229.6 279.707 229.756 279.912C230.068 280.32 230.514 280.907 231.05 281.62C232.124 283.047 233.558 284.978 235.003 286.997C236.448 289.017 237.901 291.12 239.013 292.893C239.57 293.779 240.038 294.578 240.376 295.239C240.721 295.915 240.91 296.405 240.945 296.69C240.955 296.772 240.935 296.901 240.837 297.093C240.742 297.281 240.585 297.502 240.364 297.752C239.923 298.254 239.264 298.835 238.445 299.466C236.81 300.727 234.589 302.151 232.348 303.494C230.108 304.835 227.856 306.088 226.164 307.007C225.318 307.466 224.612 307.842 224.118 308.102C223.871 308.232 223.677 308.334 223.545 308.403L223.394 308.481L223.37 308.493L223.36 308.496C223.334 308.504 223.295 308.516 223.243 308.532C223.139 308.563 222.984 308.609 222.783 308.666C222.381 308.782 221.794 308.947 221.054 309.141C219.574 309.531 217.484 310.04 215.046 310.521C210.172 311.482 203.929 312.323 198.385 311.87C198.281 311.841 198.176 311.813 198.069 311.784C196.06 311.238 193.45 310.529 191.757 308.588C190.926 307.636 190.906 306.357 191.133 305.247C191.244 304.699 191.413 304.213 191.554 303.863C191.624 303.689 191.687 303.549 191.732 303.454C191.755 303.407 191.773 303.37 191.785 303.347L191.798 303.32L191.801 303.314L191.802 303.313L191.802 303.313L191.802 303.313L191.905 303.12L191.775 302.943L191.494 303.149C191.775 302.943 191.775 302.943 191.775 302.943L191.775 302.943L191.774 302.941L191.768 302.933L191.746 302.903L191.661 302.786C191.586 302.684 191.475 302.533 191.335 302.34C191.054 301.955 190.652 301.401 190.167 300.73C189.197 299.387 187.896 297.574 186.574 295.691C185.252 293.808 183.91 291.858 182.855 290.239C182.328 289.43 181.874 288.707 181.532 288.118C181.183 287.519 180.967 287.088 180.892 286.851L180.859 286.744L180.77 286.677C174.585 281.998 170.414 272.951 167.634 263.567C164.86 254.201 163.493 244.579 162.867 238.837C162.567 236.084 164.586 233.674 167.357 233.373L171.613 232.91C174.491 232.598 176.914 230.617 177.794 227.859L180.875 218.199L180.905 218.2L181.716 218.201C182.417 218.202 183.425 218.204 184.652 218.206C187.106 218.211 190.436 218.216 193.936 218.219C200.345 218.225 207.331 218.225 210.577 218.205ZM231.78 278.614L239.315 275.674L239.32 275.671L239.349 275.659L239.404 275.635L239.467 275.608C239.571 275.562 239.726 275.492 239.925 275.4C240.325 275.214 240.906 274.935 241.628 274.561C243.073 273.814 245.081 272.69 247.332 271.183C251.837 268.166 257.295 263.629 261.155 257.532L261.162 257.521L261.169 257.511C263.048 254.967 264.979 252.777 266.79 250.855C267.38 249.68 267.37 248.742 267.226 248.106C267.152 247.781 267.043 247.53 266.953 247.362C266.909 247.278 266.869 247.216 266.842 247.176C266.833 247.162 266.826 247.152 266.82 247.144L266.812 247.133L266.807 247.126L266.806 247.125L266.806 247.125L266.806 247.125L266.806 247.125L266.805 247.124L266.783 247.097L266.767 247.064L267.078 246.907L266.767 247.064L266.767 247.064L266.766 247.063L266.766 247.062L266.764 247.058L266.755 247.041L266.723 246.976C266.695 246.92 266.653 246.836 266.601 246.73C266.497 246.517 266.349 246.212 266.173 245.84C265.826 245.104 265.37 244.105 264.932 243.048C261.858 238.511 259.339 235.372 257.285 233.231C256.548 232.463 255.43 231.546 254.115 230.535C253.351 229.947 252.53 229.334 251.687 228.705C251.08 228.252 250.461 227.791 249.843 227.323C248.374 226.211 246.922 225.075 245.696 223.978C244.536 222.942 243.557 221.922 242.959 220.976L242.833 220.963C242.638 220.943 242.352 220.913 241.986 220.876C241.253 220.8 240.2 220.693 238.918 220.564C236.354 220.306 232.873 219.962 229.209 219.618C222.516 218.991 215.236 218.369 211.786 218.271C213.331 229.046 218.295 244.059 222.915 256.47C225.257 262.76 227.506 268.374 229.171 272.415C230.003 274.435 230.689 276.062 231.166 277.184C231.405 277.744 231.592 278.179 231.719 278.473C231.741 278.524 231.761 278.571 231.78 278.614Z" fill="url(#paint8_linear_199_3380)"/>
</g>
<mask id="mask2_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="162" y="217" width="107" height="96">
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.288 217.745L180.366 217.501L180.621 217.502L180.62 217.85C180.621 217.502 180.621 217.502 180.621 217.502L180.622 217.502L180.626 217.502L180.639 217.502L180.694 217.502L180.906 217.503L181.717 217.504C182.418 217.506 183.427 217.507 184.654 217.509C187.107 217.514 190.436 217.519 193.937 217.522C200.94 217.528 208.626 217.528 211.372 217.502L211.679 217.499L211.689 217.571C215.134 217.66 222.529 218.292 229.274 218.924C232.94 219.268 236.422 219.612 238.988 219.87C240.271 219.999 241.324 220.107 242.057 220.182C242.424 220.22 242.71 220.25 242.905 220.27L243.127 220.293L243.184 220.299L243.198 220.3L243.202 220.301L243.202 220.301C243.203 220.301 243.203 220.301 243.167 220.648L243.466 220.47C243.996 221.362 244.951 222.377 246.16 223.459C247.364 224.536 248.798 225.658 250.264 226.767C250.872 227.228 251.488 227.688 252.095 228.141L252.097 228.142C252.945 228.774 253.775 229.393 254.54 229.982C255.855 230.994 257.011 231.938 257.788 232.748C259.882 234.931 262.432 238.113 265.529 242.686L265.549 242.716L265.563 242.749C265.999 243.805 266.455 244.805 266.803 245.543C266.977 245.911 267.124 246.213 267.227 246.423C267.278 246.528 267.319 246.61 267.347 246.665L267.375 246.721C267.387 246.737 267.402 246.759 267.42 246.786C267.459 246.844 267.511 246.927 267.568 247.034C267.682 247.248 267.816 247.557 267.906 247.953C268.086 248.749 268.077 249.871 267.388 251.216L267.365 251.26L267.331 251.296C265.524 253.213 263.604 255.389 261.737 257.915C257.806 264.119 252.266 268.717 247.72 271.762C245.444 273.286 243.412 274.423 241.949 275.181C241.217 275.559 240.627 275.843 240.219 276.032C240.015 276.127 239.856 276.198 239.748 276.245C239.694 276.269 239.653 276.287 239.625 276.299L239.593 276.313L239.584 276.316L239.582 276.317L239.581 276.318C239.581 276.318 239.581 276.318 239.446 275.996L239.581 276.318L239.573 276.321L239.573 276.321L232.058 279.253L232.127 279.409L231.66 279.409L231.416 279.504L231.371 279.409L230.249 279.409L230.309 279.489C230.622 279.898 231.07 280.487 231.607 281.201C232.683 282.631 234.12 284.567 235.57 286.591C237.018 288.615 238.48 290.732 239.604 292.522C240.165 293.417 240.645 294.235 240.996 294.922C241.341 295.595 241.585 296.184 241.637 296.606C241.67 296.878 241.589 297.152 241.459 297.408C241.327 297.668 241.13 297.937 240.888 298.213C240.403 298.763 239.703 299.376 238.87 300.018C237.203 301.304 234.954 302.745 232.706 304.092C230.455 305.44 228.194 306.698 226.497 307.62C225.648 308.08 224.94 308.457 224.443 308.719C224.195 308.849 224 308.951 223.867 309.021L223.714 309.1L223.675 309.12L223.665 309.125L223.663 309.127L223.662 309.127C223.662 309.127 223.662 309.127 223.502 308.817C223.605 309.15 223.605 309.15 223.605 309.15L223.604 309.15L223.602 309.151L223.594 309.154L223.563 309.163C223.536 309.171 223.495 309.184 223.442 309.2C223.336 309.231 223.179 309.278 222.976 309.336C222.57 309.453 221.977 309.619 221.232 309.815C219.741 310.208 217.636 310.721 215.18 311.205C210.275 312.172 203.943 313.029 198.295 312.561L198.263 312.559L198.232 312.55C198.11 312.517 197.984 312.483 197.854 312.448C195.875 311.912 193.07 311.153 191.232 309.046C190.187 307.849 190.207 306.294 190.45 305.107C190.572 304.507 190.755 303.979 190.907 303.602C190.977 303.43 191.04 303.289 191.089 303.185C191.043 303.123 190.986 303.045 190.918 302.952C190.874 302.891 190.825 302.824 190.772 302.751C190.49 302.364 190.087 301.81 189.602 301.138C188.631 299.794 187.328 297.978 186.004 296.092C184.68 294.206 183.332 292.248 182.271 290.62C181.741 289.806 181.28 289.072 180.929 288.469C180.621 287.939 180.384 287.488 180.265 287.168C173.936 282.327 169.739 273.127 166.966 263.764C164.174 254.342 162.802 244.674 162.174 238.912C161.831 235.759 164.147 233.02 167.282 232.68L171.537 232.218C174.142 231.935 176.334 230.143 177.13 227.647L180.288 217.745ZM223.502 308.817L223.605 309.15L223.635 309.141L223.662 309.127L223.502 308.817ZM210.577 218.205C211.831 229.119 216.979 244.304 221.816 256.772C224.256 263.061 226.621 268.669 228.376 272.705C229.254 274.723 229.979 276.348 230.485 277.469C230.725 278.001 230.915 278.419 231.05 278.712L229.542 278.712L228.832 278.712L229.266 279.273L229.542 279.06C229.266 279.273 229.266 279.273 229.266 279.274L229.267 279.274L229.268 279.276L229.274 279.284L229.299 279.316L229.394 279.439C229.477 279.548 229.6 279.707 229.756 279.912C230.068 280.32 230.514 280.907 231.05 281.62C232.124 283.047 233.558 284.978 235.003 286.997C236.448 289.017 237.901 291.12 239.013 292.893C239.57 293.779 240.038 294.578 240.376 295.239C240.721 295.915 240.91 296.405 240.945 296.69C240.955 296.772 240.935 296.901 240.837 297.093C240.742 297.281 240.585 297.502 240.364 297.752C239.923 298.254 239.264 298.835 238.445 299.466C236.81 300.727 234.589 302.151 232.348 303.494C230.108 304.835 227.856 306.088 226.164 307.007C225.318 307.466 224.612 307.842 224.118 308.102C223.871 308.232 223.677 308.334 223.545 308.403L223.394 308.481L223.37 308.493L223.36 308.496C223.334 308.504 223.295 308.516 223.243 308.532C223.139 308.563 222.984 308.609 222.783 308.666C222.381 308.782 221.794 308.947 221.054 309.141C219.574 309.531 217.484 310.04 215.046 310.521C210.172 311.482 203.929 312.323 198.385 311.87C198.281 311.841 198.176 311.813 198.069 311.784C196.06 311.238 193.45 310.529 191.757 308.588C190.926 307.636 190.906 306.357 191.133 305.247C191.244 304.699 191.413 304.213 191.554 303.863C191.624 303.689 191.687 303.549 191.732 303.454C191.755 303.407 191.773 303.37 191.785 303.347L191.798 303.32L191.801 303.314L191.802 303.313L191.802 303.313L191.802 303.313L191.905 303.12L191.775 302.943L191.494 303.149C191.775 302.943 191.775 302.943 191.775 302.943L191.775 302.943L191.774 302.941L191.768 302.933L191.746 302.903L191.661 302.786C191.586 302.684 191.475 302.533 191.335 302.34C191.054 301.955 190.652 301.401 190.167 300.73C189.197 299.387 187.896 297.574 186.574 295.691C185.252 293.808 183.91 291.858 182.855 290.239C182.328 289.43 181.874 288.707 181.532 288.118C181.183 287.519 180.967 287.088 180.892 286.851L180.859 286.744L180.77 286.677C174.585 281.998 170.414 272.951 167.634 263.567C164.86 254.201 163.493 244.579 162.867 238.837C162.567 236.084 164.586 233.674 167.357 233.373L171.613 232.91C174.491 232.598 176.914 230.617 177.794 227.859L180.875 218.199L180.905 218.2L181.716 218.201C182.417 218.202 183.425 218.204 184.652 218.206C187.106 218.211 190.436 218.216 193.936 218.219C200.345 218.225 207.331 218.225 210.577 218.205ZM231.78 278.614L239.315 275.674L239.32 275.671L239.349 275.659L239.404 275.635L239.467 275.608C239.571 275.562 239.726 275.492 239.925 275.4C240.325 275.214 240.906 274.935 241.628 274.561C243.073 273.814 245.081 272.69 247.332 271.183C251.837 268.166 257.295 263.629 261.155 257.532L261.162 257.521L261.169 257.511C263.048 254.967 264.979 252.777 266.79 250.855C267.38 249.68 267.37 248.742 267.226 248.106C267.152 247.781 267.043 247.53 266.953 247.362C266.909 247.278 266.869 247.216 266.842 247.176C266.833 247.162 266.826 247.152 266.82 247.144L266.812 247.133L266.807 247.126L266.806 247.125L266.806 247.125L266.806 247.125L266.806 247.125L266.805 247.124L266.783 247.097L266.767 247.064L267.078 246.907L266.767 247.064L266.767 247.064L266.766 247.063L266.766 247.062L266.764 247.058L266.755 247.041L266.723 246.976C266.695 246.92 266.653 246.836 266.601 246.73C266.497 246.517 266.349 246.212 266.173 245.84C265.826 245.104 265.37 244.105 264.932 243.048C261.858 238.511 259.339 235.372 257.285 233.231C256.548 232.463 255.43 231.546 254.115 230.535C253.351 229.947 252.53 229.334 251.687 228.705C251.08 228.252 250.461 227.791 249.843 227.323C248.374 226.211 246.922 225.075 245.696 223.978C244.536 222.942 243.557 221.922 242.959 220.976L242.833 220.963C242.638 220.943 242.352 220.913 241.986 220.876C241.253 220.8 240.2 220.693 238.918 220.564C236.354 220.306 232.873 219.962 229.209 219.618C222.516 218.991 215.236 218.369 211.786 218.271C213.331 229.046 218.295 244.059 222.915 256.47C225.257 262.76 227.506 268.374 229.171 272.415C230.003 274.435 230.689 276.062 231.166 277.184C231.405 277.744 231.592 278.179 231.719 278.473C231.741 278.524 231.761 278.571 231.78 278.614Z" fill="white"/>
</mask>
<g mask="url(#mask2_199_3380)">
<g filter="url(#filter11_f_199_3380)">
<ellipse cx="186.891" cy="281.806" rx="16.063" ry="16.111" transform="rotate(-7 186.891 281.806)" fill="white"/>
</g>
<g filter="url(#filter12_f_199_3380)">
<ellipse cx="261.999" cy="249.326" rx="10.4759" ry="10.5072" transform="rotate(-7 261.999 249.326)" fill="#CECECA"/>
</g>
<g filter="url(#filter13_f_199_3380)">
<ellipse cx="233.542" cy="287.702" rx="10.4759" ry="10.5072" transform="rotate(-7 233.542 287.702)" fill="#CECECA"/>
</g>
<g opacity="0.563804" filter="url(#filter14_f_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M179.832 286.529C179.832 286.529 189.781 288.751 198.322 288.953C187.522 290.457 181.165 289.169 181.165 289.169L179.832 286.529Z" fill="#DCDAD4"/>
</g>
<g filter="url(#filter15_f_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M189.041 301.263C189.041 301.263 207.931 302.388 224.342 301.623C203.392 304.374 191.306 303.789 191.306 303.789L189.041 301.263Z" fill="#DCDAD4"/>
</g>
</g>
<g filter="url(#filter16_f_199_3380)">
<ellipse cx="205.417" cy="246.907" rx="29.3325" ry="29.4202" fill="white"/>
</g>
<g filter="url(#filter17_if_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M195.9 272.571C195.9 272.571 208.836 284.91 229.48 280.222C248.859 274.567 255.131 255.587 255.131 255.587L195.9 272.571Z" fill="url(#paint9_linear_199_3380)"/>
</g>
<g filter="url(#filter18_diii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M187.671 245.094C187.473 243.549 188.533 242.224 190.049 241.867C195.311 240.627 207.623 237.86 216.472 236.93C225.31 236.001 237.503 236.188 242.729 236.327C244.249 236.367 245.537 237.402 245.706 238.913C246.413 245.222 245.777 261.583 221.396 266.216C193.407 268.376 188.514 251.697 187.671 245.094Z" fill="url(#paint10_linear_199_3380)"/>
</g>
<g filter="url(#filter19_d_199_3380)">
<path d="M190.986 246.693C190.787 245.364 191.711 244.187 193.082 243.875C197.951 242.766 208.736 240.428 216.513 239.611C224.28 238.794 234.947 238.874 239.774 238.965C241.146 238.991 242.276 239.915 242.398 241.223C242.655 243.989 242.508 248.654 239.677 253.111C236.854 257.555 231.326 261.851 220.707 263.825C208.5 264.781 201.298 261.902 197.042 258.117C192.778 254.325 191.416 249.574 190.986 246.693Z" stroke="#E4E4E4" stroke-width="0.696969"/>
</g>
<path d="M190.986 246.693C190.953 246.473 190.951 246.26 190.975 246.055L190.629 246.014C190.688 245.519 190.883 245.063 191.179 244.677L191.456 244.889C191.712 244.557 192.056 244.28 192.463 244.088L192.314 243.773C192.531 243.671 192.762 243.59 193.005 243.535L193.082 243.875C193.298 243.826 193.525 243.774 193.762 243.721L193.686 243.381C194.11 243.285 194.57 243.182 195.059 243.074L195.135 243.414C195.568 243.318 196.026 243.218 196.504 243.114L196.43 242.773C196.87 242.678 197.329 242.579 197.802 242.478L197.875 242.819C198.32 242.724 198.778 242.628 199.248 242.53L199.177 242.189C199.626 242.095 200.085 242 200.553 241.905L200.622 242.246C201.073 242.154 201.532 242.062 201.997 241.969L201.929 241.627C202.382 241.537 202.842 241.446 203.306 241.356L203.372 241.698C203.828 241.61 204.287 241.522 204.75 241.434L204.686 241.092C205.143 241.005 205.604 240.92 206.067 240.835L206.129 241.178C206.588 241.094 207.048 241.011 207.509 240.93L207.448 240.587C207.91 240.506 208.371 240.426 208.832 240.348L208.89 240.691C209.353 240.613 209.814 240.537 210.273 240.463L210.217 240.119C210.683 240.044 211.146 239.971 211.605 239.902L211.658 240.246C212.124 240.175 212.586 240.108 213.044 240.043L212.995 239.698C213.465 239.632 213.929 239.569 214.386 239.51L214.431 239.855C214.902 239.795 215.365 239.738 215.819 239.687L215.779 239.34C216.014 239.314 216.247 239.288 216.477 239.264L216.513 239.611C216.737 239.587 216.964 239.564 217.192 239.542L217.159 239.195C217.606 239.152 218.062 239.112 218.525 239.074L218.553 239.421C219.002 239.384 219.457 239.35 219.917 239.318L219.893 238.971C220.345 238.939 220.803 238.91 221.263 238.883L221.284 239.231C221.736 239.204 222.192 239.18 222.651 239.157L222.633 238.809C223.089 238.787 223.546 238.766 224.004 238.747L224.019 239.095C224.474 239.077 224.931 239.059 225.387 239.044L225.376 238.696C225.834 238.68 226.291 238.666 226.747 238.654L226.757 239.002C227.216 238.99 227.673 238.978 228.127 238.969L228.119 238.62C228.58 238.611 229.038 238.602 229.492 238.595L229.497 238.944C229.959 238.936 230.416 238.93 230.866 238.926L230.863 238.577C231.327 238.572 231.785 238.569 232.235 238.566L232.237 238.915C232.704 238.912 233.161 238.911 233.609 238.91L233.608 238.562C234.078 238.561 234.536 238.562 234.981 238.563L234.98 238.912C235.452 238.914 235.91 238.916 236.35 238.92L236.353 238.571C236.831 238.575 237.289 238.58 237.723 238.585L237.719 238.933C238.209 238.939 238.668 238.946 239.093 238.953L239.099 238.604C239.337 238.608 239.565 238.612 239.781 238.616L239.774 238.965C239.993 238.969 240.207 238.996 240.411 239.045L240.491 238.705C240.969 238.819 241.406 239.035 241.766 239.336L241.543 239.603C241.865 239.872 242.114 240.214 242.26 240.613L242.587 240.492C242.668 240.711 242.722 240.944 242.745 241.19L242.398 241.223C242.418 241.441 242.436 241.671 242.45 241.912L242.798 241.892C242.824 242.329 242.838 242.803 242.833 243.305L242.485 243.302C242.481 243.744 242.462 244.208 242.425 244.692L242.773 244.719C242.737 245.172 242.686 245.642 242.614 246.125L242.27 246.073C242.203 246.519 242.12 246.976 242.016 247.441L242.356 247.517C242.255 247.966 242.136 248.422 241.995 248.884L241.662 248.783C241.53 249.215 241.38 249.652 241.208 250.092L241.532 250.219C241.364 250.65 241.175 251.083 240.965 251.517L240.651 251.365C240.454 251.772 240.237 252.179 239.998 252.585L240.299 252.762C240.065 253.16 239.811 253.558 239.535 253.953L239.249 253.753C238.99 254.125 238.711 254.494 238.411 254.86L238.68 255.081C238.388 255.437 238.077 255.79 237.745 256.138L237.493 255.898C237.181 256.226 236.85 256.55 236.5 256.869L236.734 257.127C236.394 257.438 236.035 257.744 235.657 258.045L235.44 257.772C235.085 258.055 234.712 258.333 234.322 258.605L234.521 258.891C234.143 259.155 233.749 259.414 233.337 259.667L233.154 259.37C232.767 259.608 232.364 259.842 231.946 260.069L232.112 260.375C231.707 260.595 231.288 260.81 230.854 261.019L230.702 260.705C230.293 260.902 229.869 261.094 229.431 261.281L229.567 261.602C229.144 261.782 228.708 261.957 228.258 262.127L228.135 261.801C227.708 261.963 227.269 262.119 226.817 262.27L226.928 262.601C226.49 262.747 226.04 262.889 225.579 263.025L225.48 262.691C225.039 262.821 224.587 262.947 224.124 263.067L224.212 263.405C223.763 263.521 223.304 263.634 222.834 263.741L222.756 263.401C222.311 263.503 221.856 263.6 221.391 263.693L221.459 264.035C221.226 264.081 220.991 264.126 220.753 264.171L220.725 263.823C220.491 263.842 220.258 263.859 220.026 263.874L220.05 264.222C219.585 264.254 219.128 264.279 218.677 264.3L218.661 263.952C218.192 263.973 217.731 263.988 217.277 263.998L217.285 264.346C216.817 264.356 216.357 264.36 215.906 264.358L215.907 264.009C215.442 264.007 214.986 263.998 214.539 263.984L214.527 264.332C214.055 264.317 213.592 264.295 213.138 264.266L213.16 263.918C212.694 263.889 212.238 263.853 211.792 263.81L211.759 264.157C211.293 264.113 210.836 264.061 210.39 264.003L210.435 263.657C209.974 263.597 209.523 263.53 209.083 263.455L209.025 263.799C208.558 263.72 208.104 263.633 207.661 263.539L207.733 263.198C207.276 263.101 206.832 262.997 206.4 262.885L206.312 263.222C205.856 263.104 205.412 262.978 204.982 262.844L205.085 262.511C204.64 262.373 204.21 262.227 203.792 262.073L203.672 262.4C203.229 262.237 202.801 262.065 202.388 261.886L202.527 261.566C202.1 261.381 201.689 261.188 201.293 260.988L201.136 261.299C200.715 261.086 200.311 260.864 199.923 260.636L200.1 260.336C199.701 260.1 199.319 259.857 198.954 259.608L198.757 259.895C198.368 259.63 197.998 259.357 197.645 259.077L197.861 258.804C197.498 258.516 197.153 258.221 196.826 257.921L196.59 258.178C196.243 257.859 195.915 257.534 195.606 257.204L195.86 256.966C195.545 256.63 195.249 256.289 194.971 255.944L194.7 256.163C194.404 255.796 194.128 255.425 193.872 255.052L194.159 254.854C193.897 254.474 193.655 254.091 193.432 253.71L193.131 253.886C192.892 253.477 192.674 253.069 192.476 252.665L192.789 252.512C192.584 252.094 192.4 251.681 192.235 251.274L191.912 251.405C191.733 250.962 191.575 250.527 191.436 250.104L191.767 249.996C191.62 249.545 191.494 249.107 191.388 248.688L191.05 248.774C190.929 248.299 190.832 247.846 190.754 247.424L191.097 247.361C191.054 247.128 191.017 246.906 190.986 246.693Z" stroke="white" stroke-width="0.696969" stroke-dasharray="1.39"/>
<g filter="url(#filter20_d_199_3380)">
<g filter="url(#filter21_ii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M184.976 194.033C185.704 191.223 188.423 189.406 191.298 189.81C194.472 190.256 196.657 193.228 196.137 196.391L191.542 224.33C190.926 228.078 187.431 230.648 183.67 230.119C179.535 229.538 176.828 225.485 177.875 221.443L184.976 194.033Z" fill="url(#paint11_linear_199_3380)"/>
</g>
<path d="M184.945 197.327L185.283 195.95L185.621 196.033L185.79 195.345L185.452 195.262C185.506 195.042 185.575 194.828 185.659 194.623L185.982 194.755C186.141 194.365 186.354 194.008 186.612 193.691L186.341 193.471C186.626 193.121 186.959 192.817 187.329 192.566L187.524 192.855C187.866 192.623 188.242 192.442 188.639 192.318L188.536 191.985C188.958 191.854 189.403 191.782 189.859 191.78L189.861 192.128C190.066 192.127 190.273 192.14 190.481 192.169L190.529 191.824C190.774 191.859 191.011 191.912 191.239 191.982L191.137 192.315C191.575 192.449 191.978 192.651 192.338 192.906L192.54 192.622C192.938 192.905 193.286 193.25 193.573 193.639L193.293 193.846C193.558 194.206 193.767 194.607 193.908 195.036L194.239 194.927C194.388 195.381 194.468 195.863 194.467 196.358L194.118 196.358C194.118 196.578 194.1 196.802 194.063 197.028L194.407 197.083L194.294 197.783L193.95 197.727L193.724 199.126L194.068 199.182L193.841 200.58L193.497 200.525L193.271 201.924L193.615 201.979L193.389 203.378L193.045 203.323L192.818 204.721L193.162 204.777L192.936 206.176L192.592 206.12L192.366 207.519L192.71 207.575L192.484 208.974L192.14 208.918L191.913 210.317L192.257 210.373L192.031 211.771L191.687 211.716L191.461 213.115L191.805 213.17L191.578 214.569L191.234 214.514L191.008 215.912L191.352 215.968L191.126 217.367L190.782 217.311L190.555 218.71L190.899 218.766L190.673 220.165L190.329 220.109L190.103 221.508L190.447 221.564L190.22 222.962L189.876 222.907L189.763 223.606C189.726 223.835 189.675 224.059 189.611 224.275L189.945 224.374C189.804 224.85 189.603 225.296 189.353 225.706L189.056 225.524C188.816 225.917 188.526 226.275 188.198 226.589L188.439 226.841C188.087 227.178 187.693 227.469 187.266 227.706L187.097 227.401C186.699 227.622 186.273 227.792 185.826 227.906L185.912 228.243C185.446 228.361 184.961 228.421 184.465 228.415L184.469 228.067C184.243 228.064 184.015 228.047 183.785 228.015L183.736 228.36C183.51 228.328 183.289 228.284 183.074 228.227L183.163 227.89C182.746 227.78 182.352 227.622 181.987 227.424L181.821 227.73C181.421 227.513 181.053 227.25 180.722 226.949L180.957 226.691C180.643 226.406 180.365 226.084 180.128 225.734L179.84 225.93C179.588 225.558 179.379 225.155 179.221 224.732L179.548 224.61C179.401 224.217 179.3 223.805 179.25 223.381L178.904 223.422C178.852 222.982 178.851 222.53 178.908 222.074L179.254 222.117C179.279 221.909 179.317 221.701 179.369 221.493L179.03 221.41L179.199 220.721L179.538 220.805L179.876 219.428L179.537 219.345L179.875 217.969L180.214 218.052L180.552 216.676L180.213 216.593L180.551 215.217L180.89 215.3L181.228 213.924L180.889 213.841L181.227 212.464L181.565 212.548L181.903 211.171L181.565 211.088L181.903 209.712L182.241 209.795L182.579 208.419L182.241 208.336L182.579 206.96L182.917 207.043L183.255 205.667L182.917 205.584L183.255 204.207L183.593 204.29L183.931 202.914L183.593 202.831L183.931 201.455L184.269 201.538L184.607 200.162L184.269 200.079L184.607 198.703L184.945 198.786L185.283 197.41L184.945 197.327Z" stroke="url(#paint12_linear_199_3380)" stroke-width="0.696969" stroke-dasharray="1.39"/>
<g filter="url(#filter22_d_199_3380)">
<g filter="url(#filter23_i_199_3380)">
<ellipse cx="184.688" cy="221.774" rx="4.19036" ry="4.20288" transform="rotate(8 184.688 221.774)" fill="url(#paint13_linear_199_3380)"/>
</g>
<mask id="mask3_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="180" y="217" width="9" height="9">
<ellipse cx="184.688" cy="221.774" rx="4.19036" ry="4.20288" transform="rotate(8 184.688 221.774)" fill="white"/>
</mask>
<g mask="url(#mask3_199_3380)">
<g filter="url(#filter24_f_199_3380)">
<ellipse cx="183.748" cy="220.934" rx="2.44438" ry="2.45168" transform="rotate(8 183.748 220.934)" fill="#FFF1BC"/>
</g>
</g>
</g>
</g>
<g filter="url(#filter25_d_199_3380)">
<g filter="url(#filter26_ii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.73 196.737C236.856 193.97 234.045 192.298 231.195 192.852C228.049 193.463 226.022 196.545 226.707 199.677L232.758 227.337C233.57 231.047 237.194 233.431 240.922 232.706C245.021 231.909 247.512 227.72 246.255 223.738L237.73 196.737Z" fill="url(#paint14_linear_199_3380)"/>
</g>
<path d="M237.933 200.025L237.524 198.668L237.19 198.769L236.985 198.091L237.319 197.99C237.253 197.773 237.173 197.563 237.078 197.363L236.763 197.511C236.584 197.13 236.352 196.784 236.078 196.481L236.337 196.248C236.035 195.913 235.686 195.627 235.303 195.396L235.123 195.694C234.77 195.481 234.385 195.319 233.982 195.217L234.068 194.879C233.639 194.77 233.191 194.722 232.735 194.743L232.751 195.091C232.547 195.1 232.341 195.125 232.135 195.165L232.068 194.823C231.826 194.87 231.592 194.935 231.368 195.017L231.487 195.344C231.057 195.501 230.665 195.724 230.319 195.998L230.102 195.725C229.72 196.028 229.39 196.39 229.123 196.794L229.414 196.986C229.168 197.359 228.981 197.771 228.862 198.206L228.526 198.115C228.401 198.576 228.346 199.062 228.374 199.557L228.722 199.538C228.734 199.758 228.763 199.981 228.812 200.204L228.471 200.277L228.621 200.97L228.961 200.896L229.26 202.281L228.92 202.355L229.219 203.74L229.56 203.667L229.859 205.052L229.518 205.125L229.817 206.51L230.158 206.437L230.457 207.822L230.117 207.896L230.416 209.281L230.756 209.207L231.056 210.592L230.715 210.666L231.014 212.051L231.355 211.977L231.654 213.362L231.313 213.436L231.613 214.821L231.953 214.748L232.252 216.133L231.912 216.206L232.211 217.591L232.552 217.518L232.851 218.903L232.51 218.976L232.81 220.362L233.15 220.288L233.449 221.673L233.109 221.747L233.408 223.132L233.749 223.058L234.048 224.443L233.707 224.517L234.006 225.902L234.347 225.828L234.497 226.521C234.546 226.748 234.608 226.968 234.684 227.181L234.355 227.297C234.521 227.765 234.745 228.2 235.016 228.596L235.304 228.399C235.564 228.779 235.871 229.121 236.216 229.418L235.988 229.682C236.357 230 236.767 230.27 237.205 230.484L237.358 230.171C237.766 230.371 238.202 230.519 238.653 230.608L238.586 230.95C239.057 231.044 239.544 231.078 240.04 231.046L240.017 230.698C240.243 230.684 240.47 230.655 240.698 230.611L240.764 230.953C240.988 230.909 241.207 230.853 241.419 230.785L241.312 230.453C241.723 230.322 242.108 230.144 242.462 229.926L242.644 230.224C243.032 229.986 243.386 229.703 243.7 229.386L243.453 229.141C243.751 228.839 244.012 228.504 244.23 228.142L244.528 228.322C244.761 227.937 244.947 227.525 245.083 227.093L244.751 226.989C244.877 226.589 244.956 226.172 244.984 225.746L245.332 225.769C245.36 225.326 245.337 224.875 245.257 224.423L244.914 224.483C244.877 224.278 244.828 224.072 244.766 223.866L245.1 223.766L244.895 223.087L244.562 223.188L244.152 221.831L244.486 221.731L244.076 220.374L243.743 220.475L243.333 219.118L243.667 219.017L243.257 217.661L242.923 217.762L242.514 216.405L242.848 216.304L242.438 214.948L242.104 215.048L241.695 213.692L242.028 213.591L241.619 212.234L241.285 212.335L240.876 210.979L241.209 210.878L240.8 209.521L240.466 209.622L240.057 208.265L240.39 208.165L239.981 206.808L239.647 206.909L239.238 205.552L239.571 205.451L239.162 204.095L238.828 204.196L238.419 202.839L238.752 202.738L238.343 201.382L238.009 201.482L237.6 200.126L237.933 200.025Z" stroke="url(#paint15_linear_199_3380)" stroke-width="0.696969" stroke-dasharray="1.39"/>
<g filter="url(#filter27_d_199_3380)">
<g filter="url(#filter28_i_199_3380)">
<ellipse rx="4.19036" ry="4.20288" transform="matrix(-0.981627 0.190809 0.190809 0.981627 239.469 224.425)" fill="url(#paint16_linear_199_3380)"/>
</g>
<mask id="mask4_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="235" y="220" width="9" height="9">
<ellipse rx="4.19036" ry="4.20288" transform="matrix(-0.981627 0.190809 0.190809 0.981627 239.469 224.425)" fill="white"/>
</mask>
<g mask="url(#mask4_199_3380)">
<g filter="url(#filter29_f_199_3380)">
<ellipse rx="2.44438" ry="2.45168" transform="matrix(-0.981627 0.190809 0.190809 0.981627 240.364 223.538)" fill="#FFF1BC"/>
</g>
</g>
</g>
</g>
<g opacity="0.674878" filter="url(#filter30_f_199_3380)">
<ellipse cx="217.306" cy="204.459" rx="35.4543" ry="12.432" fill="#FF9913"/>
</g>
<g filter="url(#filter31_ii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M261.958 72.1733C261.958 72.1733 316.627 43.7326 324.797 60.3943C332.966 77.056 293.78 144.14 293.78 144.14L261.958 72.1733Z" fill="url(#paint17_linear_199_3380)"/>
</g>
<g filter="url(#filter32_di_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M263.18 84.0136C263.18 84.0136 305.86 61.8099 312.238 74.8177C318.616 87.8255 288.023 140.198 288.023 140.198L263.18 84.0136Z" fill="url(#paint18_radial_199_3380)"/>
</g>
<g filter="url(#filter33_ii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M192.808 54.9323C192.808 54.9323 157.89 4.15507 142.854 15.0311C127.819 25.9072 130.924 103.536 130.924 103.536L192.808 54.9323Z" fill="url(#paint19_linear_199_3380)"/>
</g>
<g filter="url(#filter34_di_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M186.17 64.8132C186.17 64.8132 158.91 25.1713 147.172 33.6622C135.434 42.1532 137.858 102.758 137.858 102.758L186.17 64.8132Z" fill="url(#paint20_radial_199_3380)"/>
</g>
<g filter="url(#filter35_ii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M117.269 115.411C117.269 115.411 146.956 21.806 230.265 40.2379C327.182 64.3016 300.509 160.808 300.509 160.808C300.509 160.808 308.16 234.621 195.572 209.392C93.4588 185.326 117.269 115.411 117.269 115.411Z" fill="url(#paint21_linear_199_3380)"/>
</g>
<mask id="mask5_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="114" y="37" width="190" height="178">
<path fill-rule="evenodd" clip-rule="evenodd" d="M117.269 115.411C117.269 115.411 146.956 21.806 230.265 40.2379C327.182 64.3016 300.509 160.808 300.509 160.808C300.509 160.808 308.16 234.621 195.572 209.392C93.4588 185.326 117.269 115.411 117.269 115.411Z" fill="white"/>
</mask>
<g mask="url(#mask5_199_3380)">
<g filter="url(#filter36_f_199_3380)">
<ellipse cx="217.111" cy="101.58" rx="48.5914" ry="48.6434" transform="rotate(14 217.111 101.58)" fill="#FFF172"/>
</g>
<g filter="url(#filter37_ddii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M168.527 127.87C168.527 127.87 213.429 119.183 278.596 156.701C331.85 183.59 318.945 103.774 237.896 83.0019C182.819 70.4751 160.731 94.9139 150.735 105.08C134.908 126.752 168.527 127.87 168.527 127.87Z" fill="url(#paint22_linear_199_3380)"/>
</g>
<g filter="url(#filter38_ddiii_199_3380)">
<ellipse cx="290.483" cy="172.655" rx="14.7185" ry="14.7342" transform="rotate(14 290.483 172.655)" fill="url(#paint23_linear_199_3380)"/>
</g>
</g>
<g opacity="0.377893" filter="url(#filter39_f_199_3380)">
<ellipse cx="234.766" cy="121.087" rx="38.4682" ry="38.5094" transform="rotate(14 234.766 121.087)" fill="#C59B23"/>
</g>
<g filter="url(#filter40_diii_199_3380)">
<ellipse cx="171.076" cy="129.949" rx="20.2464" ry="20.2681" transform="rotate(14 171.076 129.949)" fill="url(#paint24_linear_199_3380)"/>
</g>
<g filter="url(#filter41_ddii_199_3380)">
<ellipse cx="227.695" cy="164.786" rx="41.9005" ry="36.8356" transform="rotate(14 227.695 164.786)" fill="url(#paint25_linear_199_3380)"/>
</g>
<g opacity="0.546139" filter="url(#filter42_f_199_3380)">
<ellipse cx="224.725" cy="151.017" rx="24.3775" ry="24.4036" transform="rotate(14 224.725 151.017)" fill="#FFF372"/>
</g>
<g filter="url(#filter43_d_199_3380)">
<g filter="url(#filter44_i_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M224.915 131.298C224.578 129.809 224.859 128.227 226.044 127.265C228.1 125.596 232.45 123.6 240.619 125.637C248.24 127.537 251.523 131.036 252.929 133.517C253.886 135.207 253.273 137.192 251.9 138.566C248.548 141.917 241.71 147.734 235.833 146.269C228.759 144.675 225.893 135.624 224.915 131.298Z" fill="url(#paint26_linear_199_3380)"/>
</g>
<mask id="mask6_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="224" y="124" width="30" height="23">
<path fill-rule="evenodd" clip-rule="evenodd" d="M224.915 131.298C224.578 129.809 224.859 128.227 226.044 127.265C228.1 125.596 232.45 123.6 240.619 125.637C248.24 127.537 251.523 131.036 252.929 133.517C253.886 135.207 253.273 137.192 251.9 138.566C248.548 141.917 241.71 147.734 235.833 146.269C228.759 144.675 225.893 135.624 224.915 131.298Z" fill="white"/>
</mask>
<g mask="url(#mask6_199_3380)">
<g opacity="0.865593" filter="url(#filter45_f_199_3380)">
<ellipse cx="237.934" cy="131.392" rx="8.28801" ry="5.0649" transform="rotate(14 237.934 131.392)" fill="#BC9324"/>
</g>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M242.412 47.9299C241.919 47.4782 242.2 46.6867 242.861 46.5905C247.227 45.9558 259.277 43.6884 264.035 37.5166C271.159 26.222 277.669 53.5799 260.586 56.0825C251.974 55.8451 245.057 50.3542 242.412 47.9299Z" fill="url(#paint27_linear_199_3380)"/>
<g filter="url(#filter46_di_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M291.665 97.6877C291.665 97.6877 280.09 93.8984 283.862 89.8409C287.635 85.7835 292.321 95.5107 292.588 96.7474C292.72 97.6735 291.665 97.6877 291.665 97.6877Z" fill="url(#paint28_linear_199_3380)"/>
</g>
<g filter="url(#filter47_di_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M199.143 69.5604C199.143 69.5604 211.143 71.6492 209.716 66.2954C208.29 60.9417 199.586 67.3303 198.77 68.2971C198.218 69.0525 199.143 69.5604 199.143 69.5604Z" fill="url(#paint29_linear_199_3380)"/>
</g>
<g filter="url(#filter48_d_199_3380)">
<ellipse rx="15.1947" ry="19.3192" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 206.554 104.166)" fill="url(#paint30_linear_199_3380)"/>
<g filter="url(#filter49_i_199_3380)">
<ellipse rx="11.5111" ry="13.8134" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 203.72 106.702)" fill="url(#paint31_linear_199_3380)"/>
</g>
<mask id="mask7_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="192" y="92" width="24" height="29">
<ellipse rx="11.5111" ry="13.8134" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 203.72 106.702)" fill="white"/>
</mask>
<g mask="url(#mask7_199_3380)">
<ellipse rx="6.18547" ry="5.6093" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 210.46 102.067)" fill="white"/>
<ellipse rx="1.76728" ry="1.60266" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 200.144 107.215)" fill="white"/>
</g>
</g>
<g filter="url(#filter50_d_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M289.477 129.375C279.217 120.221 269.32 117.58 260.024 121.736C259.353 122.036 258.949 122.689 258.934 123.379C258.892 123.475 258.858 123.577 258.832 123.683C258.592 124.672 259.198 125.668 260.186 125.908C270.114 128.321 276.476 132.508 279.43 138.407C279.886 139.316 280.992 139.684 281.902 139.229C282.811 138.773 283.179 137.667 282.724 136.757C279.776 130.871 274.155 126.547 265.956 123.747C272.503 122.653 279.482 125.394 287.024 132.123C287.783 132.8 288.948 132.734 289.625 131.975C290.302 131.216 290.236 130.052 289.477 129.375Z" fill="#A47603"/>
</g>
<g filter="url(#filter51_di_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M215.422 158.836C215.422 158.836 219.344 147.036 235.687 153.039C248.584 158.717 245.096 165.398 245.096 165.398C245.096 165.398 228.388 196.697 215.956 193.033C206.798 188.691 215.422 158.836 215.422 158.836Z" fill="url(#paint32_linear_199_3380)"/>
</g>
<mask id="mask8_199_3380" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="211" y="151" width="35" height="43">
<path fill-rule="evenodd" clip-rule="evenodd" d="M215.422 158.836C215.422 158.836 219.344 147.036 235.687 153.039C248.584 158.717 245.096 165.398 245.096 165.398C245.096 165.398 228.388 196.697 215.956 193.033C206.798 188.691 215.422 158.836 215.422 158.836Z" fill="white"/>
</mask>
<g mask="url(#mask8_199_3380)">
<g filter="url(#filter52_i_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M217.319 144.977C217.319 144.977 220.267 156.208 232.33 160.777C244.664 163.735 254.513 155.642 254.513 155.642L217.319 144.977Z" fill="white"/>
</g>
<g filter="url(#filter53_di_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M204.201 186.429C204.201 186.429 211.542 181.642 220.807 186.154C230.073 190.666 230.738 198.841 230.738 198.841C230.738 198.841 214.013 196.571 212.774 193.623C208.974 191.313 204.201 186.429 204.201 186.429Z" fill="url(#paint33_linear_199_3380)"/>
</g>
</g>
<g opacity="0.479592" filter="url(#filter54_f_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M150.449 166.264L171.425 167.422C173.8 167.553 175.219 164.82 173.746 162.952L135.897 114.966H117.27L150.449 166.264Z" fill="#E69415"/>
</g>
<g filter="url(#filter55_dii_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M164.434 224.425C164.434 224.425 103.433 206.056 101.428 150.768C102.532 129.203 118.643 121.881 130.485 129.218C147.97 134.383 151.742 167.864 158.676 180.975C168.415 200.63 174.428 205.778 174.428 205.778C174.428 205.778 180.441 215.289 172.154 223.715C169.598 225.395 164.434 224.425 164.434 224.425Z" fill="url(#paint34_linear_199_3380)"/>
</g>
<g opacity="0.600254" filter="url(#filter56_f_199_3380)">
<ellipse cx="126.046" cy="148.889" rx="24.4036" ry="11.5111" fill="#E59015"/>
</g>
<g filter="url(#filter57_i_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M107.21 114.966L105.034 132.204L150.878 152.64L156.911 154.922L152.359 149.33L107.21 114.966Z" fill="url(#paint35_linear_199_3380)"/>
</g>
<g filter="url(#filter58_i_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9896 145.282C69.9896 145.282 80.3462 127.36 85.7871 125.098C111.887 129.236 155.985 153.922 155.985 153.922L107.421 115.943C106.711 115.389 107.138 114.25 108.037 114.298L131.616 115.558C131.895 115.573 132.152 115.713 132.315 115.94L163.256 159.003C163.745 159.684 163.174 160.619 162.352 160.455C149.082 157.796 81.8834 144.588 69.9896 147.311C69.7672 146.392 69.9896 145.282 69.9896 145.282Z" fill="url(#paint36_linear_199_3380)"/>
</g>
<g opacity="0.713536" filter="url(#filter59_f_199_3380)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M217.858 313.923C217.858 313.923 228.678 316.952 234.98 305.335C241.281 293.717 239.38 294.233 239.38 294.233L217.858 313.923Z" fill="url(#paint37_linear_199_3380)"/>
</g>
<defs>
<filter id="filter0_f_199_3380" x="26.6406" y="-4.09669" width="269.138" height="283.527" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="13.4334" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter1_f_199_3380" x="-181.193" y="90.659" width="315.347" height="383.116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="21.2695" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter2_f_199_3380" x="-86.4584" y="-57.966" width="200.615" height="231.632" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="21.2695" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter3_i_199_3380" x="242.613" y="178.594" width="56.6133" height="49.1921" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.97137 0 0 0 0 0.814862 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter4_ii_199_3380" x="180.56" y="272.914" width="69.8325" height="57.0051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.76267"/>
<feGaussianBlur stdDeviation="2.76267"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.737255 0 0 0 0 0.152941 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.84178"/>
<feGaussianBlur stdDeviation="3.22312"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.564706 0 0 0 0 0.0470588 0 0 0 0.729268 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter5_iii_199_3380" x="233.907" y="239.663" width="39.1635" height="39.6814" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.980392 0 0 0 0 0.588235 0 0 0 0 0.0666667 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="9.2089" dy="-0.92089"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.564706 0 0 0 0 0.0666667 0 0 0 0.581312 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_3380" result="effect3_innerShadow_199_3380"/>
</filter>
<filter id="filter6_i_199_3380" x="165.983" y="187.797" width="94.7036" height="99.1574" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-6.44623"/>
<feGaussianBlur stdDeviation="6.90668"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.560784 0 0 0 0 0.0431373 0 0 0 0.398798 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter7_f_199_3380" x="159.374" y="174.093" width="96.3152" height="89.7725" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.075" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter8_f_199_3380" x="170.927" y="166.678" width="97.9209" height="51.0071" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.23889" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter9_f_199_3380" x="159.812" y="211.831" width="102.345" height="30.6005" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.89456" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter10_iii_199_3380" x="158.457" y="216.578" width="109.548" height="97.9565" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.460445"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.815313 0 0 0 0 0.815313 0 0 0 0 0.815313 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.68356" dy="-0.92089"/>
<feGaussianBlur stdDeviation="7.82757"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.836039 0 0 0 0 0.822878 0 0 0 0 0.752079 0 0 0 0.775747 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_3380" result="effect3_innerShadow_199_3380"/>
</filter>
<filter id="filter11_f_199_3380" x="151.88" y="246.749" width="70.022" height="70.1151" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.47279" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter12_f_199_3380" x="232.576" y="219.872" width="58.8463" height="58.9068" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.47279" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter13_f_199_3380" x="204.119" y="258.249" width="58.8463" height="58.9068" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.47279" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter14_f_199_3380" x="177.937" y="284.634" width="22.2794" height="6.94268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.94728" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter15_f_199_3380" x="187.146" y="299.369" width="39.0899" height="6.36675" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.94728" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter16_f_199_3380" x="157.138" y="198.541" width="96.5562" height="96.7315" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.47279" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter17_if_199_3380" x="194.005" y="252.824" width="63.0206" height="30.3557" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.68356"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feGaussianBlur stdDeviation="0.94728" result="effect2_foregroundBlur_199_3380"/>
</filter>
<filter id="filter18_diii_199_3380" x="184.884" y="235.291" width="63.7426" height="35.7211" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.819608 0 0 0 0.805743 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.92089" dy="-0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_3380" result="effect3_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089" dy="-0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.862467 0 0 0 0 0.862467 0 0 0 0 0.862467 0 0 0 0.436214 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_3380" result="effect4_innerShadow_199_3380"/>
</filter>
<filter id="filter19_d_199_3380" x="189.689" y="238.562" width="54.0659" height="27.6384" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.460445"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.845666 0 0 0 0 0.845666 0 0 0 0 0.845666 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter20_d_199_3380" x="175.808" y="188.833" width="22.248" height="44.1174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.241201 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter21_ii_199_3380" x="176.729" y="188.833" width="20.4062" height="41.3547" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.92089" dy="-0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.323654 0 0 0 0 0.431539 0 0 0 0.686191 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.578362 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter22_d_199_3380" x="179.576" y="217.571" width="10.2241" height="10.248" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.460445"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0 0 0 0 0 0 0 0 0 0.37791 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter23_i_199_3380" x="178.655" y="214.808" width="10.2241" height="11.1689" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="-2.76267"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.588235 0 0 0 0 0.0588235 0 0 0 0.912775 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter24_f_199_3380" x="173.725" y="210.904" width="20.0461" height="20.0603" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.78912" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter25_d_199_3380" x="224.732" y="191.825" width="23.6919" height="43.7732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.241201 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter26_ii_199_3380" x="225.653" y="191.825" width="21.8501" height="41.0105" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.92089" dy="-0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.323654 0 0 0 0 0.431539 0 0 0 0.686191 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.578362 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter27_d_199_3380" x="234.357" y="220.222" width="10.2251" height="10.2483" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.460445"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0 0 0 0 0 0 0 0 0 0.37791 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter28_i_199_3380" x="233.436" y="217.459" width="10.2251" height="11.1692" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="-2.76267"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.588235 0 0 0 0 0.0588235 0 0 0 0.912775 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter29_f_199_3380" x="230.34" y="213.508" width="20.0471" height="20.0603" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.78912" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter30_f_199_3380" x="177.374" y="187.55" width="79.8643" height="33.8196" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.23889" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter31_ii_199_3380" x="260.116" y="55.1597" width="65.8008" height="94.5061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="5.52534"/>
<feGaussianBlur stdDeviation="3.22312"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.945098 0 0 0 0 0.701961 0 0 0 0.897131 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.952591 0 0 0 0 0.68394 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter32_di_199_3380" x="255.813" y="61.5221" width="64.6673" height="84.2018" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.84178"/>
<feGaussianBlur stdDeviation="3.68356"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904639 0 0 0 0 0.347983 0 0 0 0 0 0 0 0 0.719446 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="5.52534"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.529412 0 0 0 0 0.00392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter33_ii_199_3380" x="128.816" y="13.5039" width="63.9912" height="95.5573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="5.52534"/>
<feGaussianBlur stdDeviation="3.22312"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.945098 0 0 0 0 0.701961 0 0 0 0.897131 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.952591 0 0 0 0 0.68394 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter34_di_199_3380" x="130.283" y="23.2611" width="63.2543" height="85.0223" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.84178"/>
<feGaussianBlur stdDeviation="3.68356"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904639 0 0 0 0 0.347983 0 0 0 0 0 0 0 0 0.719446 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="5.52534"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.529412 0 0 0 0 0.00392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter35_ii_199_3380" x="114.861" y="25.8622" width="189.01" height="192.468" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.68356"/>
<feGaussianBlur stdDeviation="3.68356"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980598 0 0 0 0 0.782269 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-11.9716"/>
<feGaussianBlur stdDeviation="6.90668"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.627451 0 0 0 0 0.0392157 0 0 0 0.641781 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_3380" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter36_f_199_3380" x="148.678" y="33.1001" width="136.867" height="136.959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.91382" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter37_ddii_199_3380" x="140.974" y="73.0643" width="175.169" height="96.4784" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="2.76267"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.0509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.961984 0 0 0 0 0.524494 0 0 0 0 0.00423549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_3380" result="effect2_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="2.76267"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.771443 0 0 0 0 0.0546035 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-8.28801"/>
<feGaussianBlur stdDeviation="3.22312"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.384314 0 0 0 0 0.25098 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_3380" result="effect4_innerShadow_199_3380"/>
</filter>
<filter id="filter38_ddiii_199_3380" x="269.314" y="153.314" width="46.0218" height="46.0494" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.92089" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_199_3380"/>
<feOffset dx="1.84178" dy="3.68356"/>
<feGaussianBlur stdDeviation="3.68356"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576471 0 0 0 0 0.0470588 0 0 0 0.656998 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089" dy="0.92089"/>
<feGaussianBlur stdDeviation="0.460445"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.560784 0 0 0 0 0.0431373 0 0 0 0.451622 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_3380" result="effect2_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6.44623" dy="5.52534"/>
<feGaussianBlur stdDeviation="4.14401"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.935122 0 0 0 0 0.669713 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="-3.68356"/>
<feGaussianBlur stdDeviation="4.14401"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.545098 0 0 0 0 0.0392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_3380" result="effect4_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.84178" dy="4.60445"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950004 0 0 0 0 0.723552 0 0 0 0.500597 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_199_3380" result="effect5_innerShadow_199_3380"/>
</filter>
<filter id="filter39_f_199_3380" x="176.458" y="62.7434" width="116.614" height="116.687" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.91382" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter40_diii_199_3380" x="142.536" y="103.231" width="57.0809" height="57.1192" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.92089" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_199_3380"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="3.68356"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576471 0 0 0 0 0.0470588 0 0 0 0.656998 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.52534"/>
<feGaussianBlur stdDeviation="4.14401"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.935122 0 0 0 0 0.669713 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.68356"/>
<feGaussianBlur stdDeviation="2.76267"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.545098 0 0 0 0 0.0392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_3380" result="effect3_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.60445"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950004 0 0 0 0 0.723552 0 0 0 0.500597 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_3380" result="effect4_innerShadow_199_3380"/>
</filter>
<filter id="filter41_ddii_199_3380" x="175.014" y="122.1" width="105.361" height="96.4224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.52534"/>
<feGaussianBlur stdDeviation="5.52534"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576132 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.584314 0 0 0 0 0.054902 0 0 0 0.825473 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_3380" result="effect2_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.92089" operator="erode" in="SourceAlpha" result="effect3_innerShadow_199_3380"/>
<feOffset dy="4.60445"/>
<feGaussianBlur stdDeviation="3.22312"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.943556 0 0 0 0 0.725479 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-7.36712"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.638854 0 0 0 0 0.0197456 0 0 0 0.11004 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_3380" result="effect4_innerShadow_199_3380"/>
</filter>
<filter id="filter42_f_199_3380" x="184.918" y="111.187" width="79.6125" height="79.6587" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7.71075" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter43_d_199_3380" x="222.94" y="124.775" width="32.2944" height="25.4099" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.769066 0 0 0 0 0.621384 0 0 0 0 0.00850142 0 0 0 0.345285 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter44_i_199_3380" x="224.782" y="122.933" width="28.6108" height="23.5681" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.84178"/>
<feGaussianBlur stdDeviation="3.68356"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.297765 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter45_f_199_3380" x="220.986" y="117.27" width="33.896" height="28.2427" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.40614" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter46_di_199_3380" x="281.265" y="87.0154" width="15.0185" height="14.3559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089" dy="0.92089"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.411765 0 0 0 0 0.0235294 0 0 0 0.695526 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.84178"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.262548 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter47_di_199_3380" x="196.752" y="62.3399" width="16.7651" height="11.3054" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089" dy="0.92089"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.411765 0 0 0 0 0.0235294 0 0 0 0.695526 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.84178"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.262548 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter48_d_199_3380" x="185.595" y="84.1096" width="38.2348" height="45.6374" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.84178" dy="2.76267"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.0509804 0 0 0 0.551709 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter49_i_199_3380" x="192.078" y="91.1514" width="24.2036" height="29.2593" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089" dy="-1.84178"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.232984 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter50_d_199_3380" x="256.017" y="117.98" width="36.8383" height="25.1274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.92089"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.175182 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
</filter>
<filter id="filter51_di_199_3380" x="208.98" y="145.811" width="39.3081" height="62.2545" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.76267"/>
<feGaussianBlur stdDeviation="1.38134"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.929412 0 0 0 0 0.447059 0 0 0 0 0.113725 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.76267" dy="14.7342"/>
<feGaussianBlur stdDeviation="7.82757"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter52_i_199_3380" x="217.319" y="144.977" width="37.1943" height="21.0549" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.36712"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.21264 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter53_di_199_3380" x="194.071" y="170.404" width="46.7967" height="34.8836" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.68356"/>
<feGaussianBlur stdDeviation="5.0649"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.190816 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.68356" dy="-4.60445"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.0969255 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
</filter>
<filter id="filter54_f_199_3380" x="112.264" y="109.96" width="67.0886" height="62.4732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.50324" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter55_dii_199_3380" x="98.6656" y="115.407" width="82.4745" height="111.139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.92089" dy="-7.36712"/>
<feGaussianBlur stdDeviation="1.84178"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.894118 0 0 0 0 0.572549 0 0 0 0 0.0823529 0 0 0 0.500458 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_3380"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_3380" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.76267" dy="-5.52534"/>
<feGaussianBlur stdDeviation="5.0649"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.584314 0 0 0 0 0.0862745 0 0 0 0.820524 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_3380"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84178"/>
<feGaussianBlur stdDeviation="3.22312"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_3380" result="effect3_innerShadow_199_3380"/>
</filter>
<filter id="filter56_f_199_3380" x="91.6291" y="127.364" width="68.833" height="43.0481" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.00648" result="effect1_foregroundBlur_199_3380"/>
</filter>
<filter id="filter57_i_199_3380" x="105.034" y="111.283" width="51.8774" height="43.6396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.68356"/>
<feGaussianBlur stdDeviation="2.30223"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.151848 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter58_i_199_3380" x="69.8906" y="113.376" width="93.5464" height="47.0976" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.92089"/>
<feGaussianBlur stdDeviation="0.92089"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.796078 0 0 0 0 0.760784 0 0 0 0 0.607843 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_3380"/>
</filter>
<filter id="filter59_f_199_3380" x="215.354" y="291.727" width="26.798" height="25.0551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.25162" result="effect1_foregroundBlur_199_3380"/>
</filter>
<linearGradient id="paint0_linear_199_3380" x1="15.2107" y1="192.485" x2="135.931" y2="239.084" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E460"/>
<stop offset="1" stop-color="#FFC028"/>
</linearGradient>
<linearGradient id="paint1_linear_199_3380" x1="17.046" y1="126.4" x2="113.423" y2="103.583" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#8F600C"/>
</linearGradient>
<linearGradient id="paint2_linear_199_3380" x1="56.3455" y1="232.593" x2="111.088" y2="161.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#3D2700"/>
</linearGradient>
<linearGradient id="paint3_linear_199_3380" x1="122.256" y1="266.86" x2="140.71" y2="209.228" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#3D2700"/>
</linearGradient>
<linearGradient id="paint4_linear_199_3380" x1="289.763" y1="183.3" x2="242.631" y2="198.797" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD56A"/>
<stop offset="1" stop-color="#FF9E26"/>
</linearGradient>
<linearGradient id="paint5_linear_199_3380" x1="273.291" y1="313.713" x2="250.578" y2="262.063" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF2A7"/>
<stop offset="1" stop-color="#FDA61C"/>
</linearGradient>
<linearGradient id="paint6_linear_199_3380" x1="275.828" y1="265.805" x2="270.628" y2="225.311" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF4D"/>
<stop offset="1" stop-color="#FF9312"/>
</linearGradient>
<linearGradient id="paint7_linear_199_3380" x1="176.036" y1="198.784" x2="170.027" y2="281.651" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E669"/>
<stop offset="1" stop-color="#FFD73B"/>
</linearGradient>
<linearGradient id="paint8_linear_199_3380" x1="167.206" y1="247.572" x2="200.28" y2="318.204" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CFCAC1"/>
</linearGradient>
<linearGradient id="paint9_linear_199_3380" x1="199.634" y1="273.387" x2="203.712" y2="287.611" gradientUnits="userSpaceOnUse">
<stop stop-color="#EFEEEA" stop-opacity="0.01"/>
<stop offset="1" stop-color="#D0CEC6"/>
</linearGradient>
<linearGradient id="paint10_linear_199_3380" x1="189.681" y1="258.915" x2="202.207" y2="282.569" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E4E3E1"/>
</linearGradient>
<linearGradient id="paint11_linear_199_3380" x1="182.679" y1="233.768" x2="198.359" y2="231.608" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AA1E9"/>
<stop offset="1" stop-color="#3693F7"/>
</linearGradient>
<linearGradient id="paint12_linear_199_3380" x1="180.348" y1="236.788" x2="193.347" y2="236.644" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EBEBEB"/>
</linearGradient>
<linearGradient id="paint13_linear_199_3380" x1="180.711" y1="223.914" x2="184.99" y2="228.104" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE364"/>
<stop offset="1" stop-color="#FEBD27"/>
</linearGradient>
<linearGradient id="paint14_linear_199_3380" x1="242.103" y1="236.298" x2="226.331" y2="234.962" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AA1E9"/>
<stop offset="1" stop-color="#3693F7"/>
</linearGradient>
<linearGradient id="paint15_linear_199_3380" x1="244.589" y1="239.192" x2="231.6" y2="239.729" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EBEBEB"/>
</linearGradient>
<linearGradient id="paint16_linear_199_3380" x1="0.213376" y1="6.34274" x2="4.49285" y2="10.5329" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE364"/>
<stop offset="1" stop-color="#FEBD27"/>
</linearGradient>
<linearGradient id="paint17_linear_199_3380" x1="302.26" y1="52.3513" x2="280.082" y2="85.525" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF38"/>
<stop offset="1" stop-color="#FFB703"/>
</linearGradient>
<radialGradient id="paint18_radial_199_3380" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(302.305 82.0734) rotate(98.4481) scale(38.4794 15.6269)">
<stop stop-color="#FFC102"/>
<stop offset="1" stop-color="#FF8D01"/>
</radialGradient>
<linearGradient id="paint19_linear_199_3380" x1="166.529" y1="18.5101" x2="170.537" y2="58.2125" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF38"/>
<stop offset="1" stop-color="#FFB703"/>
</linearGradient>
<radialGradient id="paint20_radial_199_3380" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(152.536 44.7317) rotate(109.552) scale(38.4794 15.6269)">
<stop stop-color="#FFC102"/>
<stop offset="1" stop-color="#FF8D01"/>
</radialGradient>
<linearGradient id="paint21_linear_199_3380" x1="148.137" y1="37.4781" x2="120.242" y2="193.738" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E669"/>
<stop offset="1" stop-color="#FFD73B"/>
</linearGradient>
<linearGradient id="paint22_linear_199_3380" x1="158.65" y1="57.8794" x2="141.677" y2="123.358" gradientUnits="userSpaceOnUse">
<stop stop-color="#AF8800"/>
<stop offset="1" stop-color="#604000"/>
</linearGradient>
<linearGradient id="paint23_linear_199_3380" x1="283.917" y1="167.394" x2="281.175" y2="186.018" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD048"/>
<stop offset="1" stop-color="#FF8C03"/>
</linearGradient>
<linearGradient id="paint24_linear_199_3380" x1="162.045" y1="122.712" x2="158.272" y2="148.331" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD048"/>
<stop offset="1" stop-color="#FF8C03"/>
</linearGradient>
<linearGradient id="paint25_linear_199_3380" x1="187.258" y1="141.163" x2="191.003" y2="203.814" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD435"/>
<stop offset="1" stop-color="#FFA718"/>
</linearGradient>
<linearGradient id="paint26_linear_199_3380" x1="228.182" y1="127.005" x2="224.107" y2="143.349" gradientUnits="userSpaceOnUse">
<stop stop-color="#815E00"/>
<stop offset="1" stop-color="#4A3000"/>
</linearGradient>
<linearGradient id="paint27_linear_199_3380" x1="250.512" y1="44.8732" x2="250.406" y2="54.8099" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEF5B5"/>
<stop offset="1" stop-color="#FBEB76"/>
</linearGradient>
<linearGradient id="paint28_linear_199_3380" x1="282.48" y1="88.3653" x2="281.259" y2="95.5674" gradientUnits="userSpaceOnUse">
<stop stop-color="#B18104"/>
<stop offset="1" stop-color="#5E3B00"/>
</linearGradient>
<linearGradient id="paint29_linear_199_3380" x1="211.63" y1="65.6416" x2="209.327" y2="72.5739" gradientUnits="userSpaceOnUse">
<stop stop-color="#B18104"/>
<stop offset="1" stop-color="#5E3B00"/>
</linearGradient>
<linearGradient id="paint30_linear_199_3380" x1="0" y1="0" x2="0" y2="38.6383" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DAD5C8"/>
</linearGradient>
<linearGradient id="paint31_linear_199_3380" x1="16.3508" y1="-3.34637" x2="-3.10728" y2="9.2113" gradientUnits="userSpaceOnUse">
<stop stop-color="#A37800"/>
<stop offset="1" stop-color="#6A4300"/>
</linearGradient>
<linearGradient id="paint32_linear_199_3380" x1="216.546" y1="167.672" x2="221.906" y2="196.87" gradientUnits="userSpaceOnUse">
<stop stop-color="#B34E51"/>
<stop offset="1" stop-color="#7C2628"/>
</linearGradient>
<linearGradient id="paint33_linear_199_3380" x1="209.543" y1="184.332" x2="207.564" y2="192.58" gradientUnits="userSpaceOnUse">
<stop stop-color="#F68080"/>
<stop offset="1" stop-color="#E53636"/>
</linearGradient>
<linearGradient id="paint34_linear_199_3380" x1="90.4128" y1="181.838" x2="162.474" y2="242.099" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDE97C"/>
<stop offset="1" stop-color="#FBE167"/>
</linearGradient>
<linearGradient id="paint35_linear_199_3380" x1="115.06" y1="128.156" x2="113.262" y2="143.167" gradientUnits="userSpaceOnUse">
<stop stop-color="#D5CEB4"/>
<stop offset="1" stop-color="#AAA07D"/>
</linearGradient>
<linearGradient id="paint36_linear_199_3380" x1="55.0584" y1="151.6" x2="95.5423" y2="205.161" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEFEFF"/>
<stop offset="1" stop-color="#F2EDDD"/>
</linearGradient>
<linearGradient id="paint37_linear_199_3380" x1="222.049" y1="310.671" x2="226.289" y2="314.596" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF4E" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FEA226"/>
</linearGradient>
</defs>
</svg>
