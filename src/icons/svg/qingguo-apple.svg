<svg width="307" height="279" viewBox="0 0 307 279" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_199_2524)">
<g filter="url(#filter1_i_199_2524)">
<ellipse cx="130.434" cy="222.596" rx="42.5093" ry="35.2519" transform="rotate(-12.5207 130.434 222.596)" fill="url(#paint0_linear_199_2524)"/>
</g>
<g filter="url(#filter2_i_199_2524)">
<path d="M223.415 57.7736C206.893 57.4852 191.437 62.6243 178.376 71.6847C165.64 62.174 150.359 56.4985 133.851 56.2103C88.3039 55.4153 50.6495 95.7771 49.7667 146.352C48.8839 196.927 85.0926 238.579 130.653 239.374C147.176 239.662 162.632 234.523 175.692 225.463C188.428 234.973 203.71 240.649 220.218 240.937C265.765 241.732 303.419 201.37 304.302 150.795C305.185 100.22 268.976 58.5689 223.415 57.7736Z" fill="url(#paint1_linear_199_2524)"/>
</g>
<mask id="mask0_199_2524" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="49" y="56" width="256" height="185">
<path d="M223.415 57.7736C206.893 57.4852 191.437 62.6243 178.376 71.6847C165.64 62.174 150.359 56.4985 133.851 56.2103C88.3039 55.4153 50.6495 95.7771 49.7667 146.352C48.8839 196.927 85.0926 238.579 130.653 239.374C147.176 239.662 162.632 234.523 175.692 225.463C188.428 234.973 203.71 240.649 220.218 240.937C265.765 241.732 303.419 201.37 304.302 150.795C305.185 100.22 268.976 58.5689 223.415 57.7736Z" fill="white"/>
</mask>
<g mask="url(#mask0_199_2524)">
<g opacity="0.249183" filter="url(#filter3_f_199_2524)">
<ellipse cx="129.189" cy="105.562" rx="53.1582" ry="53.0816" transform="rotate(1 129.189 105.562)" fill="white"/>
</g>
<g filter="url(#filter4_di_199_2524)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M305.04 108.487C269.645 107.869 239.626 132.718 231.729 166.463L118.846 164.493C112.146 130.493 83.0123 104.611 47.6028 103.993C6.72822 103.279 -27.006 136.503 -27.7339 178.203C-28.4618 219.903 4.09244 254.283 44.967 254.997C62.778 255.308 79.2286 249.166 92.1858 238.702L255.769 241.558C268.353 252.468 284.58 259.179 302.391 259.49C343.265 260.203 376.999 226.98 377.727 185.28C378.455 143.58 345.915 109.2 305.04 108.487ZM-8.94729 178.531C-8.38446 146.286 17.6981 120.589 49.3133 121.141C75.3926 121.596 97.0696 139.773 103.554 164.225L41.6971 163.146L40.4015 237.374C12.0893 233.396 -9.46862 208.397 -8.94729 178.531ZM316.026 167.934L314.738 241.681C341.652 237.308 362.457 213.785 362.959 185.022C363.522 152.778 338.352 126.186 306.737 125.634C280.658 125.179 258.359 142.588 251.025 166.8L316.026 167.934Z" fill="url(#paint2_linear_199_2524)"/>
</g>
</g>
<path d="M175.652 175.041L175.699 172.38C175.745 169.719 173.631 167.53 170.969 167.483L170.955 167.483C168.293 167.437 166.104 169.551 166.057 172.212L166.056 172.282C165.844 172.279 165.646 172.247 165.433 172.243C165.065 172.237 164.697 172.259 164.343 172.267L164.344 172.168C164.391 169.506 162.276 167.317 159.614 167.271L159.6 167.271C156.939 167.224 154.749 169.338 154.703 171.999L154.651 174.943C150.101 177.583 147.095 181.891 147.008 186.831C146.865 195.069 154.875 201.892 164.913 202.067C174.951 202.243 183.194 195.703 183.338 187.465C183.441 182.384 180.377 177.814 175.652 175.041Z" fill="url(#paint3_linear_199_2524)"/>
<ellipse cx="162.168" cy="179.153" rx="1.62844" ry="1.62805" transform="rotate(1 162.168 179.153)" fill="#E86195"/>
<ellipse cx="167.733" cy="179.25" rx="1.62844" ry="1.62805" transform="rotate(1 167.733 179.25)" fill="#E86195"/>
<g filter="url(#filter5_dii_199_2524)">
<path d="M144.054 184.798C142.341 184.768 140.956 186.244 141.153 187.933C143.078 204.235 156.805 217.006 173.667 217.3C190.544 217.595 204.694 205.311 207.186 189.085C207.443 187.391 206.109 185.881 204.396 185.851L144.054 184.798Z" fill="url(#paint4_linear_199_2524)"/>
</g>
<g filter="url(#filter6_i_199_2524)">
<ellipse cx="198.95" cy="51.3876" rx="33.218" ry="28.3752" transform="rotate(-42.5009 198.95 51.3876)" fill="url(#paint5_linear_199_2524)"/>
</g>
<g filter="url(#filter7_d_199_2524)">
<path d="M176.22 70.9676C176.22 70.9676 196.353 42.9016 221.476 34.8729" stroke="#72B13C" stroke-width="2.40782" stroke-linecap="round"/>
</g>
<g filter="url(#filter8_d_199_2524)">
<path d="M197.396 34.4671C197.396 34.4671 195.141 47.6524 195.26 50.5571" stroke="#72B13C" stroke-width="2.40782" stroke-linecap="round"/>
</g>
<g filter="url(#filter9_d_199_2524)">
<path d="M221.676 46.3026C221.676 46.3026 204.549 45.7488 200.844 46.2363" stroke="#72B13C" stroke-width="2.40782" stroke-linecap="round"/>
</g>
<g filter="url(#filter10_d_199_2524)">
<path d="M201.008 59.6338C201.008 59.6338 188.85 59.1667 186.264 59.6738" stroke="#72B13C" stroke-width="2.40782" stroke-linecap="round"/>
</g>
<g filter="url(#filter11_dii_199_2524)">
<ellipse cx="222.997" cy="222.856" rx="42.5047" ry="35.2538" transform="rotate(-38.5914 222.997 222.856)" fill="url(#paint6_linear_199_2524)"/>
</g>
<g filter="url(#filter12_d_199_2524)">
<ellipse cx="150.615" cy="120.403" rx="30.1048" ry="35.0669" transform="rotate(1 150.615 120.403)" fill="url(#paint7_linear_199_2524)"/>
</g>
<g filter="url(#filter13_i_199_2524)">
<ellipse cx="154.768" cy="115.265" rx="20.674" ry="25.1994" transform="rotate(1 154.768 115.265)" fill="url(#paint8_linear_199_2524)"/>
</g>
<g filter="url(#filter14_i_199_2524)">
<path d="M156.952 100.606L160.134 107.359C160.353 107.802 160.773 108.12 161.253 108.2L168.61 109.404C169.839 109.61 170.308 111.133 169.401 111.995L163.972 117.111C163.612 117.444 163.433 117.951 163.51 118.434L164.642 125.802C164.833 127.037 163.528 127.963 162.42 127.349L155.883 123.766C155.448 123.532 154.924 123.523 154.481 123.741L147.823 127.094C146.708 127.655 145.422 126.698 145.656 125.47L147.044 118.146C147.138 117.652 146.976 117.154 146.628 116.808L141.382 111.506C140.505 110.613 141.027 109.107 142.262 108.944L149.657 107.997C150.154 107.935 150.584 107.631 150.804 107.196L154.22 100.558C154.82 99.4502 156.42 99.4781 156.952 100.606Z" fill="url(#paint9_linear_199_2524)"/>
</g>
<g filter="url(#filter15_d_199_2524)">
<ellipse cx="209.348" cy="121.965" rx="30.1048" ry="35.0669" transform="rotate(1 209.348 121.965)" fill="url(#paint10_linear_199_2524)"/>
</g>
<g filter="url(#filter16_i_199_2524)">
<ellipse cx="213.499" cy="116.827" rx="20.674" ry="25.1994" transform="rotate(1 213.499 116.827)" fill="url(#paint11_linear_199_2524)"/>
</g>
<g filter="url(#filter17_i_199_2524)">
<path d="M215.685 102.168L218.867 108.921C219.086 109.364 219.505 109.683 219.985 109.762L227.343 110.966C228.571 111.172 229.04 112.695 228.133 113.557L222.705 118.673C222.345 119.006 222.166 119.513 222.242 119.996L223.374 127.364C223.565 128.599 222.26 129.525 221.152 128.911L214.615 125.328C214.18 125.094 213.656 125.085 213.213 125.304L206.555 128.656C205.441 129.217 204.154 128.26 204.388 127.033L205.777 119.708C205.87 119.214 205.709 118.716 205.361 118.37L200.114 113.068C199.237 112.175 199.759 110.669 200.994 110.506L208.39 109.559C208.886 109.497 209.317 109.193 209.537 108.758L212.952 102.121C213.552 101.012 215.152 101.04 215.685 102.168Z" fill="url(#paint12_linear_199_2524)"/>
</g>
<g filter="url(#filter18_di_199_2524)">
<path d="M172.009 148.486C171.947 148.796 171.899 149.107 171.894 149.432C171.837 152.688 174.863 155.388 178.643 155.454C182.423 155.52 185.542 152.927 185.599 149.671C185.604 149.346 185.567 149.034 185.516 148.721L172.009 148.486Z" fill="#EA4D06"/>
</g>
<g filter="url(#filter19_di_199_2524)">
<ellipse cx="178.961" cy="137.095" rx="6.49958" ry="6.49806" transform="rotate(1 178.961 137.095)" fill="url(#paint13_linear_199_2524)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.737 133.301C16.737 133.301 20.1024 150.711 20.1024 156.318C20.1024 161.924 3.99218 168.763 0.989875 158.525C-0.958574 146.765 12.9198 146.803 12.9198 146.803L10.9139 127.75C10.835 127 11.0464 126.241 11.6349 125.77C13.0014 124.675 16.0485 123.024 20.9307 124.497C27.8363 126.581 25.5531 128.105 35.4274 125.192C35.0114 128.801 30.6655 134.278 20.9307 134.018C17.4457 133.642 16.737 133.301 16.737 133.301Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M270.113 14.6482C270.113 14.6482 267.312 32.157 265.392 37.4256C263.471 42.6941 245.991 43.6183 246.676 32.9727C248.873 21.2565 261.901 26.032 261.901 26.032L266.543 7.44183C266.725 6.71077 267.183 6.0706 267.897 5.82879C269.555 5.26683 272.985 4.75425 277.069 7.80712C282.844 12.1241 280.177 12.7761 290.454 13.411C288.826 16.6608 282.867 20.3226 273.808 16.7537C270.662 15.2106 270.113 14.6482 270.113 14.6482Z" fill="white"/>
<defs>
<filter id="filter0_d_199_2524" x="40.4768" y="13.2885" width="263.842" height="247.806" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-5.56391" dy="-3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.976471 0 0 0 0 0.265345 0 0 0 0 0 0 0 0 0.270324 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter1_i_199_2524" x="88.2305" y="183.252" width="84.4062" height="74.979" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.70927"/>
<feGaussianBlur stdDeviation="4.63659"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.261842 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter2_i_199_2524" x="42.3315" y="43.2163" width="261.987" height="197.732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-7.41855" dy="-12.9825"/>
<feGaussianBlur stdDeviation="10.2005"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.327753 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08366 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter3_f_199_2524" x="28.3736" y="4.82433" width="201.63" height="201.475" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="23.8278" result="effect1_foregroundBlur_199_2524"/>
</filter>
<filter id="filter4_di_199_2524" x="-31.4554" y="100.272" width="412.903" height="162.938" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12884 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.505374 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2524"/>
</filter>
<filter id="filter5_dii_199_2524" x="137.425" y="182.942" width="73.5025" height="41.7812" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.0422491 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.85464" dy="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2524"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.70927" dy="-1.85464"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.883003 0 0 0 0 0.725758 0 0 0 0 0.041926 0 0 0 0.751077 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_2524" result="effect3_innerShadow_199_2524"/>
</filter>
<filter id="filter6_i_199_2524" x="167.848" y="20.707" width="62.2051" height="65.0706" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.303731 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter7_d_199_2524" x="171.306" y="29.9597" width="55.0826" height="45.9212" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.307899 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter8_d_199_2524" x="190.342" y="29.5539" width="11.9674" height="25.9166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.307899 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter9_d_199_2524" x="195.931" y="41.1223" width="30.6568" height="10.0934" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.307899 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter10_d_199_2524" x="181.351" y="54.5246" width="24.5689" height="10.0626" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.307899 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter11_dii_199_2524" x="179.443" y="179.053" width="87.108" height="85.751" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.356987 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="3.70927"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.204083 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2524"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5.56391"/>
<feGaussianBlur stdDeviation="5.56391"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.283165 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_2524" result="effect3_innerShadow_199_2524"/>
</filter>
<filter id="filter12_d_199_2524" x="113.089" y="81.6281" width="75.0519" height="84.968" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="3.70927"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.805006 0 0 0 0 0.251786 0 0 0 0 0 0 0 0 0.677871 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter13_i_199_2524" x="134.092" y="90.0664" width="41.3516" height="54.1058" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="4.63659"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.145672 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter14_i_199_2524" x="140.945" y="99.7434" width="28.9297" height="31.5096" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.565128 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter15_d_199_2524" x="171.822" y="83.1904" width="75.0519" height="84.968" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="3.70927"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.805006 0 0 0 0 0.251786 0 0 0 0 0 0 0 0 0.677871 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
</filter>
<filter id="filter16_i_199_2524" x="192.822" y="91.6289" width="41.3516" height="54.1058" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="4.63659"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.145672 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter17_i_199_2524" x="199.678" y="101.306" width="28.9297" height="31.5096" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.565128 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2524"/>
</filter>
<filter id="filter18_di_199_2524" x="170.038" y="144.776" width="17.4163" height="14.3885" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.85464"/>
<feGaussianBlur stdDeviation="0.927318"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.868639 0 0 0 0 0.129122 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.70927"/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.33934 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2524"/>
</filter>
<filter id="filter19_di_199_2524" x="168.752" y="126.888" width="20.4185" height="20.4149" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.199407 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2524" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85464"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2524"/>
</filter>
<linearGradient id="paint0_linear_199_2524" x1="87.9243" y1="222.597" x2="172.943" y2="222.597" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6D365"/>
<stop offset="1" stop-color="#FDA085"/>
</linearGradient>
<linearGradient id="paint1_linear_199_2524" x1="49.7667" y1="146.352" x2="304.302" y2="150.795" gradientUnits="userSpaceOnUse">
<stop stop-color="#43E97B"/>
<stop offset="1" stop-color="#38F9D7"/>
</linearGradient>
<linearGradient id="paint2_linear_199_2524" x1="-27.7339" y1="178.203" x2="377.727" y2="185.28" gradientUnits="userSpaceOnUse">
<stop stop-color="#96FBC4"/>
<stop offset="1" stop-color="#F9F586"/>
</linearGradient>
<linearGradient id="paint3_linear_199_2524" x1="157.601" y1="176.49" x2="159.21" y2="191.359" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E4D3D3"/>
</linearGradient>
<linearGradient id="paint4_linear_199_2524" x1="133.632" y1="209.473" x2="154.486" y2="233.246" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF63E"/>
<stop offset="1" stop-color="#F9E50B"/>
</linearGradient>
<linearGradient id="paint5_linear_199_2524" x1="176.97" y1="20.0384" x2="171.87" y2="69.6763" gradientUnits="userSpaceOnUse">
<stop stop-color="#D1E971"/>
<stop offset="1" stop-color="#7BC549"/>
</linearGradient>
<linearGradient id="paint6_linear_199_2524" x1="180.493" y1="222.856" x2="265.502" y2="222.856" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6D365"/>
<stop offset="1" stop-color="#FDA085"/>
</linearGradient>
<linearGradient id="paint7_linear_199_2524" x1="119.645" y1="120.171" x2="148.216" y2="171.143" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C7D2E0"/>
</linearGradient>
<linearGradient id="paint8_linear_199_2524" x1="134.094" y1="115.265" x2="175.442" y2="115.265" gradientUnits="userSpaceOnUse">
<stop stop-color="#007ADF"/>
<stop offset="1" stop-color="#00ECBC"/>
</linearGradient>
<linearGradient id="paint9_linear_199_2524" x1="136.565" y1="108.24" x2="146.117" y2="132.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFFE0"/>
<stop offset="1" stop-color="#F6FE7E"/>
</linearGradient>
<linearGradient id="paint10_linear_199_2524" x1="178.377" y1="121.734" x2="206.948" y2="172.705" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C7D2E0"/>
</linearGradient>
<linearGradient id="paint11_linear_199_2524" x1="192.825" y1="116.827" x2="234.173" y2="116.827" gradientUnits="userSpaceOnUse">
<stop stop-color="#007ADF"/>
<stop offset="1" stop-color="#00ECBC"/>
</linearGradient>
<linearGradient id="paint12_linear_199_2524" x1="195.298" y1="109.802" x2="204.85" y2="134.282" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFFE0"/>
<stop offset="1" stop-color="#F6FE7E"/>
</linearGradient>
<linearGradient id="paint13_linear_199_2524" x1="171.343" y1="135.733" x2="174.291" y2="145.068" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFBA4"/>
<stop offset="1" stop-color="#FAE82F"/>
</linearGradient>
</defs>
</svg>
