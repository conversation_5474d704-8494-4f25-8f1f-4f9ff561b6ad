<svg width="499" height="482" viewBox="0 0 499 482" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="229.327" cy="228.542" r="172.725" fill="url(#paint0_linear_199_2731)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M138.314 259.599C138.314 259.599 210.364 420.918 336.375 364.83C390.353 333.492 414.361 291.279 414.361 291.279C414.361 291.279 445.68 230.365 400.661 189.705C326.431 137.038 309.848 280.56 231.16 274.527C172.608 255.573 138.314 259.599 138.314 259.599Z" fill="url(#paint1_linear_199_2731)"/>
<mask id="mask0_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="138" y="178" width="287" height="199">
<path fill-rule="evenodd" clip-rule="evenodd" d="M138.314 259.599C138.314 259.599 210.364 420.918 336.375 364.83C390.353 333.492 414.361 291.279 414.361 291.279C414.361 291.279 445.68 230.365 400.661 189.705C326.431 137.038 309.848 280.56 231.16 274.527C172.608 255.573 138.314 259.599 138.314 259.599Z" fill="white"/>
</mask>
<g mask="url(#mask0_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M491.715 323.101C491.715 323.101 505.361 199.6 395.893 135.847C376.108 169.218 316.946 192.401 316.946 192.401C316.946 192.401 416.61 283.375 386.842 361.584C425.664 352.712 491.715 323.101 491.715 323.101Z" fill="url(#paint2_linear_199_2731)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M363.386 361.021C363.386 361.021 366.527 264.257 276.064 207.095C262.492 232.244 250.898 215.136 250.898 215.136C250.898 215.136 321.398 335.435 302.091 377.898C332.224 373.289 363.386 361.021 363.386 361.021Z" fill="url(#paint3_linear_199_2731)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.663 405.218C222.663 405.218 277.058 343.704 255.032 250.542C233.412 258.679 235.901 240.324 235.901 240.324C235.901 240.324 211.926 362.598 177.539 378.557C197.72 394.082 222.663 405.218 222.663 405.218Z" fill="url(#paint4_linear_199_2731)"/>
<g opacity="0.446167" filter="url(#filter0_f_199_2731)">
<ellipse rx="122.014" ry="114.073" transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 275.526 204.456)" fill="#563600"/>
</g>
<g opacity="0.299277" filter="url(#filter1_f_199_2731)">
<ellipse rx="158.312" ry="121.854" transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 318.263 449.434)" fill="#FDCE49"/>
</g>
<g opacity="0.275156" filter="url(#filter2_f_199_2731)">
<ellipse rx="77.844" ry="61.1452" transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 448.268 245.928)" fill="#FDCE49"/>
</g>
</g>
<g filter="url(#filter3_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M163.368 254.523C163.368 254.523 126.487 234.469 129.698 206.082C132.909 177.696 157.106 185.269 157.959 188.374C158.812 191.479 168.382 245.085 168.382 245.085L163.368 254.523Z" fill="url(#paint5_linear_199_2731)"/>
</g>
<g filter="url(#filter4_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M192.493 328.002C192.493 328.002 214.444 339.607 209.465 359.814C195.59 369.92 173.67 346.759 173.67 346.759L188.579 326.712L192.493 328.002Z" fill="url(#paint6_linear_199_2731)"/>
</g>
<g filter="url(#filter5_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M192.186 327.951C192.186 327.951 206.566 320.745 209.276 315.266C217.675 317.206 228.226 328.151 227.882 343.371C225.257 350.862 210.939 361.09 209.375 360.451C207.811 359.812 214.059 355.982 206.48 340.097C201.301 332.141 192.186 327.951 192.186 327.951Z" fill="url(#paint7_linear_199_2731)"/>
</g>
<g filter="url(#filter6_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M246.929 320.863C246.929 320.863 262.102 302.579 254.421 295.802C246.739 289.026 225.768 301.091 225.768 301.091L246.929 320.863Z" fill="url(#paint8_linear_199_2731)"/>
</g>
<g filter="url(#filter7_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M208.187 315.393C208.187 315.393 218.647 319.325 224.291 330.218C227.875 338.315 227.624 345.387 227.624 345.387C227.624 345.387 238.349 343.447 243.048 336.669C247.746 329.89 248.942 323.16 244.222 313.047C237.377 303.736 229.781 302.764 229.781 302.764C229.781 302.764 216.624 298.292 211.154 309.376C209.282 312.158 208.187 315.393 208.187 315.393Z" fill="url(#paint9_linear_199_2731)"/>
</g>
<g filter="url(#filter8_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M213.741 310.273C213.741 310.273 223.058 301.116 225.64 300.845C228.222 300.573 240.424 309.627 242.762 315.199C245.504 320.192 246.578 324.668 245.875 328.905C245.35 331.842 242.568 338.361 235.095 341.456C233.879 330.101 231.735 326.71 227.192 320.531C219.729 311.63 213.741 310.273 213.741 310.273Z" fill="url(#paint10_linear_199_2731)"/>
</g>
<g filter="url(#filter9_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M178.956 345.16C178.956 345.16 138.661 360.087 117.463 351.621C96.2642 343.156 91.3111 321.732 118.787 315.395C146.263 309.058 147.6 306.406 147.6 306.406L178.956 345.16Z" fill="url(#paint11_linear_199_2731)"/>
</g>
<g filter="url(#filter10_iii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M142.217 299.36C142.217 299.36 131.403 300.665 119.591 289.029C107.78 277.394 121.253 258.378 136.475 255.508C140.158 255.753 142.258 257.831 142.258 257.831L142.217 299.36Z" fill="url(#paint12_linear_199_2731)"/>
</g>
<g filter="url(#filter11_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M241.334 269.078C241.334 269.078 221 340.169 178.957 345.161C160.114 330.304 147.733 317.82 138.95 303.858C126.818 284.571 132.705 263.346 138.991 255.457C142.691 250.813 146.889 252.065 154.452 250.475C160.145 249.444 171.071 240.816 171.071 240.816L241.334 269.078Z" fill="url(#paint13_linear_199_2731)"/>
</g>
<mask id="mask1_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="131" y="240" width="111" height="106">
<path fill-rule="evenodd" clip-rule="evenodd" d="M241.334 269.078C241.334 269.078 221 340.169 178.957 345.161C160.114 330.304 147.733 317.82 138.95 303.858C126.818 284.571 132.705 263.346 138.991 255.457C142.691 250.813 146.889 252.065 154.452 250.475C160.145 249.444 171.071 240.816 171.071 240.816L241.334 269.078Z" fill="white"/>
</mask>
<g mask="url(#mask1_199_2731)">
<g opacity="0.753339" filter="url(#filter12_f_199_2731)">
<ellipse rx="29.7382" ry="26.2051" transform="matrix(-0.809017 -0.587785 -0.587785 0.809017 198.116 270.069)" fill="#FDF2A4"/>
</g>
<g opacity="0.502425" filter="url(#filter13_f_199_2731)">
<ellipse rx="47.2312" ry="22.2743" transform="matrix(-0.809017 -0.587785 -0.587785 0.809017 204.188 239.39)" fill="#FF9913"/>
</g>
</g>
<g filter="url(#filter14_d_199_2731)">
<g opacity="0.311393" filter="url(#filter15_f_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M223.429 314.208C223.429 314.208 226.856 311.258 225.966 308.345C225.076 305.432 220.983 300.191 220.983 300.191L221.558 282.702L171.369 246.464L156.954 253.671L145.702 251.843L142.735 254.46L223.429 314.208Z" fill="#FF9313"/>
</g>
<g filter="url(#filter16_iii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.266 286.011L222.35 285.754L222.131 285.596L221.915 285.895C222.131 285.596 222.131 285.596 222.131 285.596L222.13 285.595L222.127 285.593L222.116 285.584L222.069 285.551L221.886 285.418L221.189 284.913C220.586 284.477 219.719 283.849 218.664 283.085C216.554 281.556 213.692 279.482 210.683 277.299C204.665 272.933 198.064 268.134 195.722 266.398L195.461 266.203L195.407 266.259C192.394 264.184 185.649 260.11 179.464 256.441C176.101 254.446 172.896 252.567 170.532 251.187C169.35 250.497 168.378 249.931 167.702 249.538C167.364 249.342 167.099 249.188 166.92 249.084L166.715 248.965L166.662 248.935L166.649 248.927L166.646 248.925L166.645 248.925C166.645 248.925 166.644 248.925 166.459 249.244L166.314 248.905C165.302 249.34 163.85 249.614 162.14 249.786C160.436 249.957 158.509 250.025 156.561 250.06C155.752 250.075 154.935 250.084 154.132 250.093C153.012 250.106 151.915 250.118 150.893 250.145C149.136 250.191 147.556 250.278 146.383 250.487C143.224 251.051 139.05 252.187 133.537 254.176L133.501 254.189L133.469 254.209C132.437 254.842 131.422 255.414 130.664 255.829C130.285 256.036 129.971 256.204 129.752 256.32C129.643 256.377 129.557 256.422 129.499 256.452L129.44 256.483C129.42 256.489 129.393 256.498 129.361 256.51C129.291 256.535 129.195 256.574 129.079 256.63C128.848 256.742 128.54 256.924 128.217 257.207C127.566 257.778 126.876 258.745 126.63 260.328L126.622 260.38L126.629 260.432C126.988 263.204 127.282 266.268 127.313 269.599C126.827 277.371 128.723 284.772 130.732 290.22C131.738 292.948 132.775 295.192 133.56 296.754C133.953 297.536 134.283 298.147 134.516 298.564C134.632 298.772 134.724 298.932 134.787 299.04C134.799 299.061 134.81 299.079 134.819 299.096C134.836 299.124 134.849 299.146 134.859 299.164L134.878 299.195L134.883 299.204L134.885 299.206L134.885 299.207C134.885 299.207 134.885 299.207 135.201 299.015L134.885 299.207L134.89 299.215L134.89 299.215L139.518 306.42L139.363 306.511L139.765 306.803L139.914 307.035L140.011 306.982L140.975 307.683L140.874 307.713C140.35 307.869 139.6 308.094 138.693 308.371C136.88 308.925 134.44 309.687 131.935 310.518C129.432 311.349 126.858 312.25 124.779 313.084C123.74 313.5 122.819 313.902 122.089 314.272C121.374 314.634 120.798 314.986 120.491 315.315C120.293 315.528 120.191 315.814 120.144 316.115C120.096 316.42 120.097 316.774 120.134 317.161C120.207 317.936 120.427 318.898 120.742 319.968C121.374 322.112 122.408 324.751 123.501 327.309C124.595 329.87 125.753 332.36 126.637 334.21C127.079 335.135 127.453 335.9 127.716 336.434C127.848 336.701 127.952 336.91 128.023 337.053L128.105 337.216L128.126 337.257L128.131 337.268L128.133 337.271L128.133 337.271C128.133 337.272 128.133 337.272 128.155 337.261L128.133 337.272L128.148 337.301L128.167 337.327L128.463 337.105C128.167 337.327 128.167 337.327 128.168 337.327L128.168 337.327L128.169 337.329L128.175 337.336L128.195 337.364C128.214 337.388 128.241 337.424 128.276 337.471C128.348 337.564 128.454 337.702 128.592 337.879C128.868 338.233 129.273 338.745 129.792 339.379C130.828 340.646 132.316 342.4 134.123 344.348C137.734 348.24 142.639 352.927 147.78 356.052L147.809 356.07L147.841 356.082C147.967 356.129 148.096 356.179 148.229 356.23C150.262 357.006 153.143 358.107 156.033 357.448C157.677 357.074 158.627 355.728 159.158 354.56C159.426 353.968 159.597 353.401 159.701 352.983C159.749 352.793 159.783 352.632 159.805 352.513C159.934 352.472 160.117 352.413 160.348 352.339C160.83 352.183 161.521 351.96 162.356 351.687C164.027 351.141 166.276 350.397 168.587 349.607C170.898 348.817 173.274 347.98 175.199 347.246C176.161 346.88 177.014 346.538 177.691 346.24C178.285 345.979 178.769 345.74 179.071 345.54C187.519 345.341 196.849 340.075 205.059 333.781C213.321 327.446 220.518 320.015 224.644 315.467C226.903 312.978 226.623 309.187 224.147 306.942L220.763 303.873C218.707 302.009 217.943 299.11 218.812 296.475L222.266 286.011ZM195.967 267.497C188.096 276.07 174.223 285.873 162.308 293.542C156.297 297.41 150.775 300.741 146.756 303.105C144.746 304.287 143.112 305.227 141.98 305.872C141.443 306.178 141.019 306.418 140.721 306.585L142.016 307.526L142.625 307.969L141.902 308.18L141.799 307.825C141.902 308.18 141.902 308.18 141.902 308.18L141.902 308.18L141.899 308.181L141.889 308.184L141.848 308.196L141.69 308.242C141.55 308.283 141.346 308.344 141.085 308.421C140.563 308.577 139.814 308.801 138.909 309.078C137.099 309.631 134.666 310.391 132.168 311.219C129.67 312.048 127.112 312.944 125.054 313.77C124.024 314.182 123.125 314.575 122.423 314.931C121.706 315.294 121.239 315.597 121.031 315.819C120.972 315.882 120.909 316.006 120.873 316.231C120.839 316.452 120.836 316.739 120.869 317.092C120.936 317.797 121.141 318.707 121.451 319.759C122.07 321.861 123.09 324.468 124.18 327.019C125.269 329.567 126.422 332.047 127.303 333.891C127.744 334.813 128.117 335.575 128.379 336.107C128.51 336.373 128.614 336.581 128.684 336.723L128.765 336.884L128.778 336.91L128.785 336.918L128.82 336.965L128.864 337.022C128.934 337.114 129.038 337.249 129.174 337.424C129.447 337.774 129.849 338.282 130.363 338.911C131.392 340.169 132.87 341.911 134.665 343.845C138.253 347.712 143.091 352.33 148.134 355.403C148.24 355.443 148.348 355.484 148.457 355.526L148.459 355.527C150.525 356.313 153.207 357.335 155.869 356.728C157.175 356.431 157.989 355.347 158.485 354.254C158.73 353.715 158.888 353.193 158.985 352.805C159.033 352.611 159.065 352.452 159.086 352.343C159.096 352.288 159.103 352.246 159.108 352.218L159.112 352.187L159.113 352.18L159.114 352.179L159.114 352.179L159.114 352.179L159.146 351.948L159.367 351.878L159.48 352.229C159.367 351.878 159.367 351.877 159.367 351.877L159.368 351.877L159.37 351.877L159.38 351.874L159.417 351.862L159.564 351.815C159.692 351.774 159.88 351.713 160.121 351.635C160.602 351.48 161.293 351.257 162.127 350.984C163.796 350.439 166.041 349.697 168.348 348.908C170.656 348.119 173.023 347.285 174.936 346.556C175.893 346.192 176.732 345.855 177.393 345.564C178.065 345.268 178.519 345.034 178.731 344.877L178.826 344.806L178.944 344.804C187.168 344.654 196.381 339.503 204.61 333.194C212.822 326.898 219.984 319.504 224.097 314.971C226.069 312.798 225.839 309.474 223.651 307.489L220.266 304.421C217.995 302.361 217.15 299.156 218.111 296.244L221.479 286.036L221.453 286.017L220.755 285.512C220.152 285.075 219.285 284.447 218.23 283.683C216.12 282.154 213.258 280.08 210.249 277.897C204.742 273.902 198.742 269.541 195.967 267.497ZM140.156 306.045L135.517 298.823L135.516 298.822L135.515 298.82L135.512 298.815L135.494 298.786C135.479 298.76 135.456 298.721 135.425 298.668C135.364 298.564 135.275 298.408 135.161 298.204C134.933 297.796 134.608 297.193 134.22 296.422C133.445 294.88 132.42 292.662 131.425 289.965C129.434 284.566 127.571 277.269 128.051 269.632L128.052 269.619L128.052 269.605C128.022 266.252 127.727 263.169 127.368 260.39C127.593 259.016 128.185 258.217 128.704 257.762C128.97 257.53 129.22 257.383 129.402 257.295C129.492 257.251 129.565 257.222 129.613 257.204C129.637 257.196 129.655 257.19 129.665 257.187L129.674 257.184L129.675 257.184L129.675 257.184L129.676 257.183L129.676 257.183L129.676 257.183L129.712 257.174L129.747 257.156L129.579 256.827L129.748 257.156L129.748 257.156L129.748 257.155L129.749 257.155L129.754 257.152L129.771 257.143L129.84 257.108C129.857 257.099 129.877 257.088 129.9 257.077C129.953 257.049 130.019 257.014 130.097 256.973C130.319 256.856 130.636 256.686 131.019 256.477C131.775 256.063 132.789 255.492 133.823 254.858C139.295 252.886 143.415 251.767 146.513 251.215C147.625 251.016 149.155 250.929 150.912 250.883C151.933 250.857 153.016 250.845 154.129 250.832L154.131 250.832C154.934 250.823 155.753 250.814 156.575 250.799C158.527 250.763 160.479 250.696 162.214 250.521C163.854 250.356 165.329 250.093 166.432 249.655L166.549 249.723C166.729 249.827 166.993 249.981 167.331 250.177C168.007 250.57 168.978 251.135 170.16 251.825C172.523 253.205 175.726 255.083 179.087 257.076C185.225 260.717 191.865 264.728 194.889 266.799C186.854 275.071 173.246 284.842 161.553 292.597C155.626 296.528 150.2 299.935 146.255 302.36C144.283 303.572 142.681 304.539 141.573 305.202C141.019 305.534 140.588 305.79 140.295 305.963C140.245 305.993 140.198 306.02 140.156 306.045Z" fill="url(#paint14_linear_199_2731)"/>
</g>
<mask id="mask2_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="120" y="248" width="107" height="110">
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.266 286.011L222.35 285.754L222.131 285.596L221.915 285.895C222.131 285.596 222.131 285.596 222.131 285.596L222.13 285.595L222.127 285.593L222.116 285.584L222.069 285.551L221.886 285.418L221.189 284.913C220.586 284.477 219.719 283.849 218.664 283.085C216.554 281.556 213.692 279.482 210.683 277.299C204.665 272.933 198.064 268.134 195.722 266.398L195.461 266.203L195.407 266.259C192.394 264.184 185.649 260.11 179.464 256.441C176.101 254.446 172.896 252.567 170.532 251.187C169.35 250.497 168.378 249.931 167.702 249.538C167.364 249.342 167.099 249.188 166.92 249.084L166.715 248.965L166.662 248.935L166.649 248.927L166.646 248.925L166.645 248.925C166.645 248.925 166.644 248.925 166.459 249.244L166.314 248.905C165.302 249.34 163.85 249.614 162.14 249.786C160.436 249.957 158.509 250.025 156.561 250.06C155.752 250.075 154.935 250.084 154.132 250.093C153.012 250.106 151.915 250.118 150.893 250.145C149.136 250.191 147.556 250.278 146.383 250.487C143.224 251.051 139.05 252.187 133.537 254.176L133.501 254.189L133.469 254.209C132.437 254.842 131.422 255.414 130.664 255.829C130.285 256.036 129.971 256.204 129.752 256.32C129.643 256.377 129.557 256.422 129.499 256.452L129.44 256.483C129.42 256.489 129.393 256.498 129.361 256.51C129.291 256.535 129.195 256.574 129.079 256.63C128.848 256.742 128.54 256.924 128.217 257.207C127.566 257.778 126.876 258.745 126.63 260.328L126.622 260.38L126.629 260.432C126.988 263.204 127.282 266.268 127.313 269.599C126.827 277.371 128.723 284.772 130.732 290.22C131.738 292.948 132.775 295.192 133.56 296.754C133.953 297.536 134.283 298.147 134.516 298.564C134.632 298.772 134.724 298.932 134.787 299.04C134.799 299.061 134.81 299.079 134.819 299.096C134.836 299.124 134.849 299.146 134.859 299.164L134.878 299.195L134.883 299.204L134.885 299.206L134.885 299.207C134.885 299.207 134.885 299.207 135.201 299.015L134.885 299.207L134.89 299.215L134.89 299.215L139.518 306.42L139.363 306.511L139.765 306.803L139.914 307.035L140.011 306.982L140.975 307.683L140.874 307.713C140.35 307.869 139.6 308.094 138.693 308.371C136.88 308.925 134.44 309.687 131.935 310.518C129.432 311.349 126.858 312.25 124.779 313.084C123.74 313.5 122.819 313.902 122.089 314.272C121.374 314.634 120.798 314.986 120.491 315.315C120.293 315.528 120.191 315.814 120.144 316.115C120.096 316.42 120.097 316.774 120.134 317.161C120.207 317.936 120.427 318.898 120.742 319.968C121.374 322.112 122.408 324.751 123.501 327.309C124.595 329.87 125.753 332.36 126.637 334.21C127.079 335.135 127.453 335.9 127.716 336.434C127.848 336.701 127.952 336.91 128.023 337.053L128.105 337.216L128.126 337.257L128.131 337.268L128.133 337.271L128.133 337.271C128.133 337.272 128.133 337.272 128.155 337.261L128.133 337.272L128.148 337.301L128.167 337.327L128.463 337.105C128.167 337.327 128.167 337.327 128.168 337.327L128.168 337.327L128.169 337.329L128.175 337.336L128.195 337.364C128.214 337.388 128.241 337.424 128.276 337.471C128.348 337.564 128.454 337.702 128.592 337.879C128.868 338.233 129.273 338.745 129.792 339.379C130.828 340.646 132.316 342.4 134.123 344.348C137.734 348.24 142.639 352.927 147.78 356.052L147.809 356.07L147.841 356.082C147.967 356.129 148.096 356.179 148.229 356.23C150.262 357.006 153.143 358.107 156.033 357.448C157.677 357.074 158.627 355.728 159.158 354.56C159.426 353.968 159.597 353.401 159.701 352.983C159.749 352.793 159.783 352.632 159.805 352.513C159.934 352.472 160.117 352.413 160.348 352.339C160.83 352.183 161.521 351.96 162.356 351.687C164.027 351.141 166.276 350.397 168.587 349.607C170.898 348.817 173.274 347.98 175.199 347.246C176.161 346.88 177.014 346.538 177.691 346.24C178.285 345.979 178.769 345.74 179.071 345.54C187.519 345.341 196.849 340.075 205.059 333.781C213.321 327.446 220.518 320.015 224.644 315.467C226.903 312.978 226.623 309.187 224.147 306.942L220.763 303.873C218.707 302.009 217.943 299.11 218.812 296.475L222.266 286.011ZM195.967 267.497C188.096 276.07 174.223 285.873 162.308 293.542C156.297 297.41 150.775 300.741 146.756 303.105C144.746 304.287 143.112 305.227 141.98 305.872C141.443 306.178 141.019 306.418 140.721 306.585L142.016 307.526L142.625 307.969L141.902 308.18L141.799 307.825C141.902 308.18 141.902 308.18 141.902 308.18L141.902 308.18L141.899 308.181L141.889 308.184L141.848 308.196L141.69 308.242C141.55 308.283 141.346 308.344 141.085 308.421C140.563 308.577 139.814 308.801 138.909 309.078C137.099 309.631 134.666 310.391 132.168 311.219C129.67 312.048 127.112 312.944 125.054 313.77C124.024 314.182 123.125 314.575 122.423 314.931C121.706 315.294 121.239 315.597 121.031 315.819C120.972 315.882 120.909 316.006 120.873 316.231C120.839 316.452 120.836 316.739 120.869 317.092C120.936 317.797 121.141 318.707 121.451 319.759C122.07 321.861 123.09 324.468 124.18 327.019C125.269 329.567 126.422 332.047 127.303 333.891C127.744 334.813 128.117 335.575 128.379 336.107C128.51 336.373 128.614 336.581 128.684 336.723L128.765 336.884L128.778 336.91L128.785 336.918L128.82 336.965L128.864 337.022C128.934 337.114 129.038 337.249 129.174 337.424C129.447 337.774 129.849 338.282 130.363 338.911C131.392 340.169 132.87 341.911 134.665 343.845C138.253 347.712 143.091 352.33 148.134 355.403C148.24 355.443 148.348 355.484 148.457 355.526L148.459 355.527C150.525 356.313 153.207 357.335 155.869 356.728C157.175 356.431 157.989 355.347 158.485 354.254C158.73 353.715 158.888 353.193 158.985 352.805C159.033 352.611 159.065 352.452 159.086 352.343C159.096 352.288 159.103 352.246 159.108 352.218L159.112 352.187L159.113 352.18L159.114 352.179L159.114 352.179L159.114 352.179L159.146 351.948L159.367 351.878L159.48 352.229C159.367 351.878 159.367 351.877 159.367 351.877L159.368 351.877L159.37 351.877L159.38 351.874L159.417 351.862L159.564 351.815C159.692 351.774 159.88 351.713 160.121 351.635C160.602 351.48 161.293 351.257 162.127 350.984C163.796 350.439 166.041 349.697 168.348 348.908C170.656 348.119 173.023 347.285 174.936 346.556C175.893 346.192 176.732 345.855 177.393 345.564C178.065 345.268 178.519 345.034 178.731 344.877L178.826 344.806L178.944 344.804C187.168 344.654 196.381 339.503 204.61 333.194C212.822 326.898 219.984 319.504 224.097 314.971C226.069 312.798 225.839 309.474 223.651 307.489L220.266 304.421C217.995 302.361 217.15 299.156 218.111 296.244L221.479 286.036L221.453 286.017L220.755 285.512C220.152 285.075 219.285 284.447 218.23 283.683C216.12 282.154 213.258 280.08 210.249 277.897C204.742 273.902 198.742 269.541 195.967 267.497ZM140.156 306.045L135.517 298.823L135.516 298.822L135.515 298.82L135.512 298.815L135.494 298.786C135.479 298.76 135.456 298.721 135.425 298.668C135.364 298.564 135.275 298.408 135.161 298.204C134.933 297.796 134.608 297.193 134.22 296.422C133.445 294.88 132.42 292.662 131.425 289.965C129.434 284.566 127.571 277.269 128.051 269.632L128.052 269.619L128.052 269.605C128.022 266.252 127.727 263.169 127.368 260.39C127.593 259.016 128.185 258.217 128.704 257.762C128.97 257.53 129.22 257.383 129.402 257.295C129.492 257.251 129.565 257.222 129.613 257.204C129.637 257.196 129.655 257.19 129.665 257.187L129.674 257.184L129.675 257.184L129.675 257.184L129.676 257.183L129.676 257.183L129.676 257.183L129.712 257.174L129.747 257.156L129.579 256.827L129.748 257.156L129.748 257.156L129.748 257.155L129.749 257.155L129.754 257.152L129.771 257.143L129.84 257.108C129.857 257.099 129.877 257.088 129.9 257.077C129.953 257.049 130.019 257.014 130.097 256.973C130.319 256.856 130.636 256.686 131.019 256.477C131.775 256.063 132.789 255.492 133.823 254.858C139.295 252.886 143.415 251.767 146.513 251.215C147.625 251.016 149.155 250.929 150.912 250.883C151.933 250.857 153.016 250.845 154.129 250.832L154.131 250.832C154.934 250.823 155.753 250.814 156.575 250.799C158.527 250.763 160.479 250.696 162.214 250.521C163.854 250.356 165.329 250.093 166.432 249.655L166.549 249.723C166.729 249.827 166.993 249.981 167.331 250.177C168.007 250.57 168.978 251.135 170.16 251.825C172.523 253.205 175.726 255.083 179.087 257.076C185.225 260.717 191.865 264.728 194.889 266.799C186.854 275.071 173.246 284.842 161.553 292.597C155.626 296.528 150.2 299.935 146.255 302.36C144.283 303.572 142.681 304.539 141.573 305.202C141.019 305.534 140.588 305.79 140.295 305.963C140.245 305.993 140.198 306.02 140.156 306.045Z" fill="white"/>
</mask>
<g mask="url(#mask2_199_2731)">
<g filter="url(#filter17_f_199_2731)">
<ellipse rx="17.0555" ry="17.0678" transform="matrix(-0.731354 -0.681998 -0.681998 0.731354 176.72 336.807)" fill="white"/>
</g>
<g filter="url(#filter18_f_199_2731)">
<ellipse rx="11.1232" ry="11.1312" transform="matrix(-0.731354 -0.681998 -0.681998 0.731354 132.434 262.071)" fill="#CECECA"/>
</g>
<g filter="url(#filter19_f_199_2731)">
<ellipse rx="11.1232" ry="11.1312" transform="matrix(-0.731354 -0.681998 -0.681998 0.731354 132.986 312.736)" fill="#CECECA"/>
</g>
<g opacity="0.563804" filter="url(#filter20_f_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M179.843 345.262C179.843 345.262 169.915 340.956 162.454 335.797C170.793 343.829 177.055 346.694 177.055 346.694L179.843 345.262Z" fill="#DCDAD4"/>
</g>
<g filter="url(#filter21_f_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M162.762 352.144C162.762 352.144 145.839 341.315 132.222 330.414C148.502 345.851 159.245 352.896 159.245 352.896L162.762 352.144Z" fill="#DCDAD4"/>
</g>
</g>
<g filter="url(#filter22_f_199_2731)">
<ellipse rx="31.1449" ry="31.1672" transform="matrix(-0.809017 -0.587785 -0.587785 0.809017 182.542 295.323)" fill="white"/>
</g>
<g filter="url(#filter23_if_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M174.734 323.259C174.734 323.259 155.939 325.76 141.125 308.859C127.999 291.917 134.43 271.736 134.43 271.736L174.734 323.259Z" fill="url(#paint15_linear_199_2731)"/>
</g>
<g filter="url(#filter24_diii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M198.907 304.85C200.039 303.649 199.955 301.852 198.878 300.602C195.132 296.256 186.278 286.195 179.255 279.871C172.24 273.555 161.648 266.102 157.076 262.959C155.747 262.046 153.999 262.131 152.912 263.321C148.377 268.289 138.741 282.71 156.795 301.901C179.487 321.225 194.071 309.985 198.907 304.85Z" fill="url(#paint16_linear_199_2731)"/>
</g>
<g filter="url(#filter25_d_199_2731)">
<path d="M195.065 304.152C196.063 303.136 196.004 301.552 195.023 300.43C191.533 296.441 183.723 287.701 177.55 282.143C171.386 276.592 162.171 269.999 157.971 267.065C156.779 266.232 155.234 266.32 154.315 267.366C152.372 269.576 149.595 273.667 149.252 279.255C148.91 284.827 150.985 291.961 158.875 300.281C168.764 308.722 176.741 310.75 182.752 310.163C188.775 309.574 192.902 306.353 195.065 304.152Z" stroke="#E4E4E4" stroke-width="0.738756"/>
</g>
<path d="M195.065 304.152C195.23 303.984 195.365 303.803 195.472 303.612L195.794 303.793C196.052 303.331 196.169 302.818 196.155 302.303L195.786 302.313C195.774 301.868 195.65 301.416 195.421 300.998L195.745 300.821C195.623 300.598 195.474 300.385 195.301 300.186L195.023 300.43C194.868 300.253 194.705 300.067 194.534 299.873L194.812 299.629C194.507 299.282 194.176 298.907 193.823 298.508L193.547 298.753C193.234 298.4 192.903 298.029 192.557 297.641L192.833 297.395C192.514 297.038 192.182 296.667 191.838 296.285L191.563 296.532C191.24 296.173 190.906 295.804 190.564 295.427L190.837 295.178C190.51 294.818 190.175 294.45 189.832 294.076L189.56 294.325C189.23 293.965 188.894 293.599 188.552 293.229L188.823 292.978C188.49 292.618 188.151 292.253 187.809 291.886L187.539 292.138C187.203 291.778 186.863 291.415 186.519 291.051L186.788 290.798C186.449 290.438 186.106 290.077 185.762 289.716L185.494 289.971C185.153 289.612 184.809 289.254 184.464 288.897L184.729 288.64C184.384 288.282 184.037 287.925 183.689 287.571L183.425 287.829C183.077 287.474 182.728 287.12 182.38 286.77L182.642 286.51C182.289 286.155 181.936 285.804 181.585 285.457L181.326 285.72C180.969 285.368 180.614 285.021 180.261 284.68L180.518 284.414C180.156 284.064 179.796 283.72 179.44 283.384L179.187 283.653C178.82 283.307 178.457 282.969 178.1 282.642L178.349 282.369C178.164 282.2 177.98 282.033 177.798 281.868L177.55 282.143C177.373 281.983 177.192 281.822 177.01 281.66L177.254 281.384C176.897 281.067 176.531 280.748 176.157 280.426L175.916 280.707C175.553 280.395 175.184 280.081 174.808 279.767L175.046 279.484C174.677 279.174 174.302 278.864 173.923 278.553L173.689 278.839C173.317 278.533 172.94 278.227 172.561 277.922L172.792 277.634C172.415 277.331 172.035 277.027 171.653 276.725L171.424 277.015C171.044 276.714 170.662 276.414 170.28 276.116L170.507 275.825C170.123 275.525 169.739 275.228 169.355 274.932L169.13 275.225C168.744 274.928 168.358 274.633 167.974 274.341L168.197 274.047C167.807 273.751 167.419 273.457 167.034 273.168L166.812 273.464C166.42 273.169 166.032 272.879 165.647 272.593L165.867 272.297C165.471 272.002 165.08 271.713 164.695 271.43L164.476 271.728C164.077 271.434 163.685 271.147 163.301 270.868L163.519 270.569C163.116 270.275 162.722 269.99 162.338 269.713L162.122 270.013C161.715 269.719 161.32 269.436 160.94 269.164L161.155 268.863C160.742 268.568 160.346 268.286 159.97 268.02L159.756 268.321C159.332 268.02 158.933 267.739 158.563 267.48L158.776 267.177C158.568 267.032 158.37 266.893 158.182 266.762L157.971 267.065C157.781 266.932 157.581 266.822 157.375 266.736L157.518 266.395C157.037 266.194 156.528 266.108 156.032 266.141L156.057 266.51C155.614 266.539 155.186 266.678 154.813 266.928L154.607 266.621C154.402 266.758 154.211 266.925 154.038 267.122L154.315 267.366C154.162 267.54 154.003 267.727 153.841 267.925L153.555 267.69C153.26 268.05 152.953 268.448 152.644 268.882L152.945 269.096C152.673 269.478 152.399 269.888 152.13 270.326L151.815 270.133C151.563 270.544 151.315 270.98 151.076 271.439L151.403 271.609C151.183 272.033 150.97 272.477 150.77 272.941L150.431 272.795C150.238 273.243 150.056 273.71 149.889 274.194L150.239 274.314C150.083 274.768 149.94 275.237 149.814 275.722L149.457 275.629C149.333 276.104 149.226 276.593 149.137 277.097L149.5 277.161C149.417 277.634 149.35 278.119 149.302 278.616L148.935 278.581C148.888 279.069 148.859 279.568 148.85 280.079L149.22 280.086C149.212 280.566 149.222 281.057 149.252 281.558L148.883 281.58C148.913 282.067 148.961 282.564 149.029 283.07L149.395 283.021C149.459 283.496 149.542 283.981 149.644 284.473L149.283 284.548C149.382 285.027 149.5 285.514 149.638 286.007L149.994 285.908C150.123 286.372 150.27 286.842 150.436 287.32L150.087 287.441C150.247 287.903 150.425 288.371 150.621 288.845L150.963 288.703C151.147 289.149 151.348 289.6 151.566 290.056L151.233 290.215C151.443 290.656 151.67 291.101 151.913 291.551L152.238 291.376C152.467 291.8 152.711 292.229 152.971 292.662L152.654 292.852C152.906 293.271 153.171 293.693 153.451 294.119L153.76 293.916C154.026 294.32 154.306 294.728 154.599 295.139L154.299 295.354C154.583 295.753 154.881 296.154 155.192 296.559L155.485 296.334C155.782 296.72 156.092 297.109 156.414 297.501L156.129 297.736C156.441 298.116 156.766 298.499 157.102 298.884L157.38 298.641C157.699 299.005 158.029 299.372 158.371 299.742L158.099 299.993C158.27 300.178 158.444 300.363 158.621 300.549L158.861 300.269C159.051 300.431 159.24 300.591 159.429 300.748L159.192 301.032C159.571 301.349 159.948 301.656 160.321 301.955L160.552 301.666C160.941 301.977 161.328 302.278 161.711 302.569L161.487 302.863C161.883 303.163 162.275 303.453 162.664 303.733L162.879 303.433C163.28 303.721 163.676 303.999 164.069 304.265L163.862 304.571C164.277 304.853 164.688 305.123 165.095 305.381L165.293 305.07C165.711 305.335 166.124 305.589 166.534 305.83L166.346 306.148C166.774 306.401 167.197 306.642 167.616 306.87L167.793 306.546C168.227 306.783 168.656 307.006 169.08 307.217L168.916 307.548C169.365 307.771 169.808 307.98 170.247 308.177L170.398 307.839C170.85 308.042 171.296 308.229 171.737 308.403L171.601 308.747C172.067 308.931 172.526 309.099 172.979 309.254L173.098 308.904C173.565 309.063 174.026 309.207 174.48 309.336L174.379 309.691C174.86 309.828 175.334 309.948 175.801 310.053L175.882 309.692C176.363 309.8 176.836 309.892 177.3 309.967L177.241 310.332C177.735 310.412 178.219 310.475 178.695 310.522L178.731 310.154C179.22 310.202 179.699 310.232 180.169 310.247L180.157 310.616C180.656 310.631 181.144 310.629 181.621 310.61L181.607 310.241C182.099 310.221 182.579 310.184 183.047 310.131L183.088 310.499C183.585 310.442 184.069 310.369 184.54 310.279L184.471 309.917C184.952 309.826 185.419 309.718 185.872 309.596L185.967 309.953C186.451 309.823 186.919 309.678 187.372 309.519L187.249 309.17C187.712 309.008 188.158 308.831 188.588 308.643L188.736 308.982C189.196 308.78 189.637 308.567 190.059 308.344L189.887 308.017C190.323 307.787 190.739 307.548 191.135 307.302L191.33 307.615C191.76 307.348 192.167 307.073 192.55 306.797L192.334 306.497C192.742 306.203 193.123 305.906 193.476 305.613L193.712 305.897C194.112 305.565 194.477 305.237 194.808 304.923L194.553 304.655C194.735 304.483 194.905 304.315 195.065 304.152Z" stroke="white" stroke-width="0.738756" stroke-dasharray="1.48"/>
<g filter="url(#filter26_d_199_2731)">
<g filter="url(#filter27_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M233.025 262.777C234.153 259.909 232.95 256.652 230.229 255.205C227.223 253.607 223.491 254.796 221.964 257.839L208.519 284.619C206.711 288.222 208.109 292.61 211.668 294.503C215.583 296.585 220.438 294.792 222.061 290.665L233.025 262.777Z" fill="url(#paint17_linear_199_2731)"/>
</g>
<path d="M231 265.616L231.567 264.227L231.225 264.087L231.508 263.393L231.85 263.532C231.941 263.309 232.014 263.082 232.071 262.854L231.712 262.766C231.819 262.332 231.858 261.891 231.835 261.458L232.203 261.438C232.177 260.961 232.081 260.491 231.92 260.045L231.572 260.171C231.423 259.758 231.214 259.368 230.949 259.014L231.245 258.793C230.965 258.417 230.627 258.078 230.237 257.79L230.018 258.088C229.843 257.959 229.657 257.842 229.46 257.737L229.633 257.411C229.402 257.288 229.165 257.185 228.925 257.103L228.805 257.452C228.346 257.294 227.873 257.216 227.404 257.211L227.408 256.841C226.889 256.836 226.375 256.914 225.886 257.069L225.997 257.422C225.545 257.565 225.115 257.78 224.726 258.06L224.51 257.761C224.098 258.057 223.729 258.422 223.421 258.848L223.72 259.065C223.583 259.255 223.459 259.458 223.349 259.675L223.019 259.508L222.681 260.178L223.011 260.344L222.334 261.683L222.005 261.516L221.328 262.855L221.658 263.022L220.982 264.36L220.652 264.194L219.975 265.533L220.305 265.699L219.629 267.038L219.299 266.871L218.622 268.21L218.952 268.377L218.276 269.715L217.946 269.549L217.27 270.887L217.599 271.054L216.923 272.393L216.593 272.226L215.917 273.565L216.246 273.731L215.57 275.07L215.24 274.904L214.564 276.242L214.893 276.409L214.217 277.748L213.887 277.581L213.211 278.92L213.54 279.086L212.864 280.425L212.534 280.259L211.858 281.597L212.187 281.764L211.511 283.103L211.181 282.936L210.505 284.275L210.835 284.441L210.496 285.111C210.385 285.331 210.29 285.554 210.21 285.78L209.862 285.657C209.686 286.154 209.58 286.662 209.539 287.17L209.907 287.2C209.868 287.687 209.893 288.175 209.979 288.65L209.616 288.716C209.708 289.225 209.865 289.721 210.083 290.19L210.418 290.034C210.622 290.472 210.882 290.885 211.195 291.261L210.911 291.497C211.238 291.889 211.617 292.243 212.047 292.548L212.261 292.247C212.456 292.385 212.663 292.513 212.881 292.629L212.707 292.955C212.921 293.069 213.139 293.168 213.359 293.254L213.493 292.91C213.92 293.076 214.356 293.186 214.794 293.243L214.746 293.609C215.225 293.672 215.705 293.676 216.177 293.624L216.137 293.257C216.584 293.207 217.024 293.104 217.445 292.952L217.571 293.299C218.02 293.137 218.45 292.921 218.85 292.656L218.646 292.348C219.017 292.102 219.361 291.811 219.669 291.478L219.94 291.728C220.259 291.383 220.542 290.995 220.778 290.568L220.455 290.389C220.562 290.195 220.659 289.992 220.745 289.781L221.087 289.92L221.37 289.226L221.028 289.087L221.595 287.698L221.937 287.837L222.503 286.448L222.161 286.309L222.728 284.92L223.07 285.059L223.636 283.671L223.294 283.531L223.861 282.142L224.203 282.282L224.769 280.893L224.427 280.753L224.994 279.365L225.336 279.504L225.902 278.115L225.56 277.976L226.127 276.587L226.469 276.726L227.035 275.338L226.693 275.198L227.26 273.809L227.602 273.949L228.168 272.56L227.826 272.42L228.392 271.031L228.734 271.171L229.301 269.782L228.959 269.643L229.525 268.254L229.867 268.393L230.434 267.004L230.092 266.865L230.658 265.476L231 265.616Z" stroke="url(#paint18_linear_199_2731)" stroke-width="0.738756" stroke-dasharray="1.48"/>
<g filter="url(#filter28_d_199_2731)">
<g filter="url(#filter29_i_199_2731)">
<ellipse rx="4.44927" ry="4.45246" transform="matrix(-0.882948 -0.469472 -0.469472 0.882948 215.994 286.718)" fill="url(#paint19_linear_199_2731)"/>
</g>
<mask id="mask3_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="211" y="282" width="10" height="10">
<ellipse rx="4.44927" ry="4.45246" transform="matrix(-0.882948 -0.469472 -0.469472 0.882948 215.994 286.718)" fill="white"/>
</mask>
<g mask="url(#mask3_199_2731)">
<g filter="url(#filter30_f_199_2731)">
<ellipse rx="2.59541" ry="2.59727" transform="matrix(-0.882948 -0.469472 -0.469472 0.882948 217.324 286.585)" fill="#FFF1BC"/>
</g>
</g>
</g>
</g>
<g filter="url(#filter31_d_199_2731)">
<g filter="url(#filter32_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M186.011 232.16C188.49 230.329 191.947 230.648 194.049 232.901C196.371 235.391 196.188 239.304 193.645 241.566L171.256 261.484C168.244 264.164 163.644 263.949 160.894 261C157.87 257.757 158.345 252.604 161.911 249.969L186.011 232.16Z" fill="url(#paint20_linear_199_2731)"/>
</g>
<path d="M183.794 234.85L184.989 233.943L185.212 234.237L185.81 233.784L185.587 233.49C185.779 233.344 185.979 233.214 186.184 233.101L186.363 233.425C186.754 233.209 187.17 233.057 187.594 232.968L187.518 232.606C187.986 232.508 188.464 232.479 188.937 232.52L188.905 232.888C189.342 232.926 189.774 233.027 190.184 233.19L190.321 232.847C190.757 233.021 191.172 233.259 191.55 233.562L191.32 233.85C191.489 233.986 191.651 234.135 191.803 234.298L192.073 234.046C192.252 234.238 192.412 234.44 192.554 234.651L192.248 234.857C192.519 235.26 192.717 235.696 192.843 236.148L193.199 236.049C193.339 236.548 193.396 237.065 193.373 237.578L193.004 237.561C192.982 238.036 192.886 238.507 192.716 238.955L193.061 239.086C192.881 239.56 192.625 240.011 192.292 240.419L192.006 240.186C191.858 240.368 191.694 240.541 191.513 240.703L191.759 240.978L191.2 241.478L190.954 241.203L189.836 242.202L190.082 242.478L188.964 243.478L188.718 243.202L187.6 244.202L187.846 244.478L186.728 245.477L186.482 245.202L185.364 246.202L185.61 246.477L184.492 247.477L184.246 247.202L183.128 248.202L183.374 248.477L182.256 249.477L182.009 249.202L180.891 250.202L181.138 250.477L180.02 251.477L179.773 251.202L178.655 252.201L178.902 252.477L177.784 253.477L177.537 253.201L176.419 254.201L176.666 254.477L175.548 255.477L175.301 255.201L174.183 256.201L174.429 256.476L173.311 257.476L173.065 257.201L171.947 258.201L172.193 258.476L171.075 259.476L170.829 259.201L170.27 259.701C170.086 259.865 169.895 260.015 169.697 260.151L169.906 260.455C169.472 260.753 169.009 260.988 168.529 261.158L168.405 260.81C167.944 260.974 167.467 261.076 166.985 261.116L167.016 261.484C166.5 261.527 165.98 261.504 165.47 261.414L165.534 261.05C165.059 260.967 164.593 260.822 164.148 260.617L163.994 260.953C163.531 260.739 163.091 260.464 162.685 260.128L162.921 259.843C162.736 259.69 162.559 259.524 162.391 259.343L162.121 259.595C161.956 259.418 161.803 259.234 161.663 259.043L161.961 258.825C161.691 258.455 161.471 258.062 161.303 257.654L160.961 257.796C160.777 257.349 160.649 256.886 160.577 256.417L160.942 256.361C160.874 255.916 160.859 255.465 160.898 255.018L160.53 254.986C160.57 254.511 160.668 254.04 160.82 253.585L161.17 253.702C161.312 253.28 161.504 252.872 161.746 252.489L161.434 252.291C161.685 251.894 161.987 251.521 162.338 251.182L162.594 251.448C162.754 251.294 162.925 251.148 163.106 251.01L162.883 250.716L163.481 250.262L163.704 250.557L164.899 249.65L164.676 249.356L165.87 248.449L166.094 248.743L167.289 247.837L167.065 247.542L168.26 246.636L168.484 246.93L169.678 246.023L169.455 245.729L170.65 244.823L170.873 245.117L172.068 244.21L171.845 243.916L173.04 243.009L173.263 243.304L174.458 242.397L174.235 242.103L175.43 241.196L175.653 241.49L176.848 240.584L176.625 240.289L177.82 239.383L178.043 239.677L179.238 238.77L179.015 238.476L180.209 237.57L180.433 237.864L181.628 236.957L181.404 236.663L182.599 235.756L182.823 236.051L184.017 235.144L183.794 234.85Z" stroke="url(#paint21_linear_199_2731)" stroke-width="0.738756" stroke-dasharray="1.48"/>
<g filter="url(#filter33_d_199_2731)">
<g filter="url(#filter34_i_199_2731)">
<ellipse cx="167.295" cy="254.807" rx="4.44927" ry="4.45246" transform="rotate(47 167.295 254.807)" fill="url(#paint22_linear_199_2731)"/>
</g>
<mask id="mask4_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="162" y="250" width="10" height="10">
<ellipse cx="167.295" cy="254.807" rx="4.44927" ry="4.45246" transform="rotate(47 167.295 254.807)" fill="white"/>
</mask>
<g mask="url(#mask4_199_2731)">
<g filter="url(#filter35_f_199_2731)">
<ellipse cx="167.08" cy="253.487" rx="2.59541" ry="2.59727" transform="rotate(47 167.08 253.487)" fill="#FFF1BC"/>
</g>
</g>
</g>
</g>
</g>
<g opacity="0.674878" filter="url(#filter36_f_199_2731)">
<ellipse rx="37.6449" ry="13.1703" transform="matrix(-0.809017 -0.587785 -0.587785 0.809017 198.76 251.524)" fill="#FF9913"/>
</g>
<g filter="url(#filter37_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M242.794 110.319C242.794 110.319 213.546 51.8603 196.143 61.0425C178.739 70.2247 170.6 152.14 170.6 152.14L242.794 110.319Z" fill="url(#paint23_linear_199_2731)"/>
</g>
<g filter="url(#filter38_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M234.366 119.703C234.366 119.703 211.532 74.0637 197.945 81.2323C184.358 88.4009 178.004 152.352 178.004 152.352L234.366 119.703Z" fill="url(#paint24_radial_199_2731)"/>
</g>
<g filter="url(#filter39_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M312.953 138.666C312.953 138.666 374.601 116.932 380.742 135.626C386.882 154.321 335.834 218.9 335.834 218.9L312.953 138.666Z" fill="url(#paint25_linear_199_2731)"/>
</g>
<g filter="url(#filter40_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M312.497 151.271C312.497 151.271 360.626 134.303 365.419 148.898C370.213 163.492 330.36 213.91 330.36 213.91L312.497 151.271Z" fill="url(#paint26_radial_199_2731)"/>
</g>
<g filter="url(#filter41_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M340.165 237.589C340.165 237.589 372.995 138.873 289.927 102.718C191.656 62.9027 154.429 162.226 154.429 162.226C154.429 162.226 101.852 220.7 214.314 269.289C317.051 312.342 340.165 237.589 340.165 237.589Z" fill="url(#paint27_linear_199_2731)"/>
</g>
<mask id="mask5_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="142" y="93" width="205" height="190">
<path fill-rule="evenodd" clip-rule="evenodd" d="M340.165 237.589C340.165 237.589 372.995 138.873 289.927 102.718C191.656 62.9027 154.429 162.226 154.429 162.226C154.429 162.226 101.852 220.7 214.314 269.289C317.051 312.342 340.165 237.589 340.165 237.589Z" fill="white"/>
</mask>
<g mask="url(#mask5_199_2731)">
<g filter="url(#filter42_f_199_2731)">
<ellipse rx="51.5938" ry="51.532" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 263 163.481)" fill="#FFF172"/>
</g>
<g filter="url(#filter43_ddii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M288.359 216.302C288.359 216.302 255.192 180.859 175.817 172.371C113.301 162.203 174.134 101.861 256.718 134.601C311.849 158.212 315.597 192.926 317.849 207.869C317.942 236.308 288.359 216.302 288.359 216.302Z" fill="url(#paint28_linear_199_2731)"/>
</g>
<g filter="url(#filter44_ddiii_199_2731)">
<ellipse rx="15.6279" ry="15.6092" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 155.661 178.629)" fill="url(#paint29_linear_199_2731)"/>
</g>
</g>
<g opacity="0.377893" filter="url(#filter45_f_199_2731)">
<ellipse rx="40.8451" ry="40.7962" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 235.672 169.187)" fill="#C59B23"/>
</g>
<g filter="url(#filter46_diii_199_2731)">
<ellipse rx="21.4974" ry="21.4717" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 284.872 216.494)" fill="url(#paint30_linear_199_2731)"/>
</g>
<g filter="url(#filter47_ddii_199_2731)">
<ellipse rx="44.4895" ry="39.023" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 214.513 211.038)" fill="url(#paint31_linear_199_2731)"/>
</g>
<g opacity="0.546139" filter="url(#filter48_f_199_2731)">
<ellipse rx="25.8837" ry="25.8527" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 225.647 201.093)" fill="#FFF372"/>
</g>
<g filter="url(#filter49_d_199_2731)">
<g filter="url(#filter50_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.768 184.082C238.989 183.014 239.735 181.479 239.315 179.912C238.587 177.2 236.091 172.781 227.808 169.434C220.081 166.313 215.082 167.26 212.326 168.508C210.447 169.359 209.733 171.449 210.059 173.486C210.851 178.449 213.102 187.69 219.062 190.097C226.13 193.142 234.23 187.18 237.768 184.082Z" fill="url(#paint32_linear_199_2731)"/>
</g>
<mask id="mask6_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="209" y="167" width="31" height="24">
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.768 184.082C238.989 183.014 239.735 181.479 239.315 179.912C238.587 177.2 236.091 172.781 227.808 169.434C220.081 166.313 215.082 167.26 212.326 168.508C210.447 169.359 209.733 171.449 210.059 173.486C210.851 178.449 213.102 187.69 219.062 190.097C226.13 193.142 234.23 187.18 237.768 184.082Z" fill="white"/>
</mask>
<g mask="url(#mask6_199_2731)">
<g opacity="0.865593" filter="url(#filter51_f_199_2731)">
<ellipse rx="8.80011" ry="5.36566" transform="matrix(-0.927184 -0.374607 -0.374607 0.927184 226.528 176.04)" fill="#BC9324"/>
</g>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M274.694 101.734C275.399 101.655 275.652 100.8 275.143 100.305C271.787 97.0383 262.848 87.5823 262.606 79.328C263.524 65.2078 240.881 84.5902 253.999 97.387C261.546 102.553 270.91 102.162 274.694 101.734Z" fill="url(#paint33_linear_199_2731)"/>
<g filter="url(#filter52_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M201.365 113.656C201.365 113.656 213.673 117.628 212.96 111.799C212.247 105.969 202.159 111.382 201.159 112.275C200.468 112.986 201.365 113.656 201.365 113.656Z" fill="url(#paint34_linear_199_2731)"/>
</g>
<g filter="url(#filter53_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M298.393 147.25C298.393 147.25 286.781 141.557 291.343 137.859C295.905 134.161 299.403 145.063 299.501 146.4C299.505 147.391 298.393 147.25 298.393 147.25Z" fill="url(#paint35_linear_199_2731)"/>
</g>
<g filter="url(#filter54_d_199_2731)">
<ellipse cx="270.446" cy="172.276" rx="16.1324" ry="20.4687" transform="rotate(23 270.446 172.276)" fill="url(#paint36_linear_199_2731)"/>
<g filter="url(#filter55_i_199_2731)">
<ellipse cx="271.301" cy="176.219" rx="12.2224" ry="14.6336" transform="rotate(23 271.301 176.219)" fill="url(#paint37_linear_199_2731)"/>
</g>
<mask id="mask7_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="258" y="161" width="26" height="30">
<ellipse cx="271.301" cy="176.219" rx="12.2224" ry="14.6336" transform="rotate(23 271.301 176.219)" fill="white"/>
</mask>
<g mask="url(#mask7_199_2731)">
<ellipse cx="268.398" cy="168.044" rx="6.56766" ry="5.94239" transform="rotate(23 268.398 168.044)" fill="white"/>
<ellipse cx="274.054" cy="178.888" rx="1.87647" ry="1.69783" transform="rotate(23 274.054 178.888)" fill="white"/>
</g>
</g>
<g filter="url(#filter56_d_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M183.515 142.14C198.036 140.693 208.186 144.601 213.583 153.957C213.972 154.632 213.912 155.442 213.496 156.042C213.472 156.152 213.439 156.261 213.394 156.368C212.985 157.365 211.843 157.84 210.844 157.43C200.81 153.308 192.732 152.93 186.517 156.143C185.559 156.638 184.378 156.263 183.881 155.306C183.383 154.349 183.757 153.171 184.715 152.676C190.918 149.47 198.442 149.269 207.231 151.981C202.288 146.963 194.583 144.96 183.909 146.024C182.834 146.131 181.875 145.348 181.767 144.276C181.658 143.203 182.441 142.247 183.515 142.14Z" fill="#A47603"/>
</g>
<g filter="url(#filter57_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M228.798 213.593C228.798 213.593 232.783 201.037 214.999 195.991C200.378 192.816 199.211 200.715 199.211 200.715C199.211 200.715 194.058 237.95 207.024 242.561C217.599 244.551 228.798 213.593 228.798 213.593Z" fill="url(#paint38_linear_199_2731)"/>
</g>
<mask id="mask8_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="198" y="195" width="32" height="48">
<path fill-rule="evenodd" clip-rule="evenodd" d="M228.798 213.593C228.798 213.593 232.783 201.037 214.999 195.991C200.378 192.816 199.211 200.715 199.211 200.715C199.211 200.715 194.058 237.95 207.024 242.561C217.599 244.551 228.798 213.593 228.798 213.593Z" fill="white"/>
</mask>
<g mask="url(#mask8_199_2731)">
<g filter="url(#filter58_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M235.807 200.536C235.807 200.536 226.273 208.321 213.06 204.715C200.619 199.559 197.2 186.484 197.2 186.484L235.807 200.536Z" fill="white"/>
</g>
<g filter="url(#filter59_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M221.241 244.233C221.241 244.233 217.917 235.554 207.144 233.643C196.37 231.732 190.703 238.323 190.703 238.323C190.703 238.323 206.489 246.806 209.391 245.052C214.096 245.441 221.241 244.233 221.241 244.233Z" fill="url(#paint39_linear_199_2731)"/>
</g>
</g>
<g opacity="0.713536" filter="url(#filter60_f_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M130.124 344.996C130.124 344.996 118.943 340.839 120.764 326.95C122.585 313.06 123.897 314.689 123.897 314.689L130.124 344.996Z" fill="url(#paint40_linear_199_2731)"/>
</g>
<g filter="url(#filter61_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M340.302 117.68C323.832 90.7805 282.219 67.64 243.828 72.3579C236.325 72.3579 229.202 67.1006 222.471 61.3478L221.489 60.503L220.512 59.6544L217.138 56.7025L216.187 55.8781L215.241 55.0681L214.301 54.2759C207.74 48.8007 201.596 44.8776 195.88 47.6257C181.713 53.1361 172.161 70.627 167.225 100.098L163.754 104.614L160.966 108.258L157.745 112.489L154.659 116.564L152.678 119.195L149.819 123.012L147.541 126.074L145.357 129.029L144.092 130.75L142.059 133.532L140.12 136.206C115.421 165.896 119.471 200.273 152.272 239.339C162.702 253.742 186.417 269.37 223.419 286.225C274.862 313.827 313.823 311.042 340.302 277.871C357.887 256.679 367.749 226.907 369.89 188.552C372.196 183.428 375.02 178.748 377.945 174.318L378.745 173.116L379.549 171.925L380.353 170.745L381.157 169.575L383.153 166.687L384.718 164.408L385.486 163.276L386.241 162.149C393.344 151.455 398.469 141.206 394.389 128.049C390.718 120.523 382.751 118.08 373.373 117.465L372.554 117.416C372.417 117.408 372.28 117.401 372.142 117.395L371.313 117.359C371.174 117.353 371.035 117.348 370.896 117.344L370.058 117.319C369.918 117.316 369.778 117.313 369.637 117.31L368.792 117.297L367.941 117.29L367.086 117.289L366.227 117.294L365.365 117.304L364.065 117.328L363.196 117.349L361.888 117.387L360.14 117.447L358.852 117.496L353.601 117.706C348.961 117.88 344.431 117.981 340.302 117.68ZM197.911 51.4653C199.792 50.6372 202.071 51.0742 205.354 53.0497L205.621 53.2114L206.083 53.4957L206.478 53.7456L206.769 53.9342L207.272 54.2717L207.679 54.5555L208.203 54.9361L208.643 55.2677L209.109 55.6291L209.607 56.0255L210.285 56.5783L211.037 57.2044L212.239 58.2236L213.432 59.2488L218.293 63.4758L219.034 64.1163L219.75 64.7298L220.78 65.6023L221.44 66.1539L222.081 66.6827L222.705 67.1901L223.313 67.6773L223.907 68.1454L224.489 68.5956L225.061 69.0293C225.156 69.1003 225.25 69.1706 225.344 69.2403L225.903 69.6513L226.457 70.0487C226.51 70.0864 226.563 70.1239 226.616 70.1612C226.654 70.1884 226.693 70.2156 226.732 70.2427L227.28 70.622L227.826 70.9908C227.917 71.0514 228.009 71.1117 228.1 71.1716L228.647 71.5268L229.198 71.8745L229.475 72.046C234.387 75.0663 239.064 76.695 243.828 76.695H244.094L244.358 76.6625L245.4 76.5421C279.827 72.81 320.069 92.9499 336.597 119.942L337.761 121.843L339.986 122.006L340.682 122.053L341.371 122.095L341.829 122.119L342.287 122.141L342.748 122.16L343.662 122.189L344.601 122.208L345.328 122.216L346.599 122.217L347.405 122.21L348.538 122.193L349.751 122.166L351.053 122.13L353.583 122.044L360.305 121.781L361.39 121.743L362.435 121.712L363.457 121.687L364.457 121.668C364.622 121.666 364.786 121.663 364.948 121.661L366.389 121.649C366.547 121.648 366.703 121.648 366.859 121.648L367.781 121.651L368.681 121.66C368.779 121.662 368.875 121.663 368.972 121.665C369.022 121.666 369.073 121.666 369.124 121.667L369.992 121.687C370.278 121.695 370.561 121.703 370.84 121.713L371.667 121.747C371.74 121.75 371.813 121.753 371.885 121.757C371.948 121.76 372.01 121.763 372.073 121.766L372.869 121.81L373.645 121.861L374.402 121.92L375.138 121.986L375.856 122.06C383.642 122.913 387.956 125.169 390.273 129.533L390.34 129.662L390.461 130.076C392.88 138.59 391.11 145.969 385.235 155.656L385.032 155.987L384.618 156.659L384.187 157.344L383.737 158.047L383.267 158.772L382.773 159.523L382.252 160.304L381.703 161.12L381.122 161.975L377.132 167.769L375.77 169.763L375.044 170.842C374.925 171.019 374.808 171.195 374.691 171.371L374.005 172.409L373.345 173.423C373.237 173.59 373.13 173.756 373.023 173.921L372.399 174.903C372.092 175.389 371.794 175.868 371.503 176.341L370.933 177.28C368.971 180.543 367.373 183.564 365.928 186.774L365.598 187.508L365.488 189.433C363.268 226.382 353.72 254.903 336.958 275.104L336.152 276.099C311.172 306.534 274.741 308.838 225.474 282.405L225.35 282.338L224.134 281.783C221.607 280.625 219.144 279.473 216.746 278.328L214.706 277.348C184.349 262.673 164.677 249.068 155.791 236.798L155.7 236.672L154.661 235.427C123.962 198.318 120.487 166.593 143.461 138.978L143.556 138.863L145.181 136.62L146.778 134.429L148.86 131.592L151.037 128.648L153.308 125.595L155.673 122.435L158.637 118.5L161.21 115.102L164.423 110.883L167.772 106.508L171.329 101.884L171.508 100.814L171.651 99.974C176.395 72.3717 185.194 56.4362 197.456 51.6669L197.612 51.6061L197.764 51.5333L197.911 51.4653Z" fill="white"/>
</g>
<g filter="url(#filter62_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M157.765 237.066C157.765 237.066 111.779 182.296 146.408 140.672C156.307 126.955 171.741 106.923 171.741 106.923C171.741 106.923 178.662 65.6039 198.524 57.8784C211.244 51.7629 226.236 80.9947 243.34 80.9947C279.222 76.585 318.116 98.2136 333.511 123.356C351.198 124.644 376.751 118.055 384.064 133.047C390.754 154.622 370.962 167.829 361.165 189.597C358.164 243.37 333.511 273.08 333.511 273.08C333.511 273.08 296.387 319.586 224.264 280.889C172.388 257.258 157.765 237.066 157.765 237.066Z" fill="white" fill-opacity="0.150539"/>
</g>
<path opacity="0.65532" d="M160.417 138.836C160.417 138.836 136.763 167.789 151.504 194.627" stroke="url(#paint41_linear_199_2731)" stroke-width="11.5715"/>
<path opacity="0.65532" d="M343.263 233.877C343.263 233.877 335.616 270.454 305.452 275.904" stroke="url(#paint42_linear_199_2731)" stroke-width="7.23219"/>
<g filter="url(#filter63_ddii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M232.558 301.311C232.558 301.311 296.792 322.467 332.179 275.707C344.342 256.322 334.784 240.171 320.02 239.336C300.247 235.973 278.015 259.945 264.006 267.106C243.575 278.24 235.213 279.051 235.213 279.051C235.213 279.051 224.194 283.648 226.275 295.999C227.476 299.012 232.558 301.311 232.558 301.311Z" fill="url(#paint43_linear_199_2731)"/>
</g>
<circle cx="160.578" cy="276.837" r="5.11356" fill="#E8E7E5" stroke="#9BC7FF" stroke-width="2.27269"/>
<circle cx="160.579" cy="276.837" r="3.12495" fill="#F97956"/>
<g filter="url(#filter64_i_199_2731)">
<circle cx="220.641" cy="339.531" r="5.11356" fill="#F0D2D2"/>
</g>
<circle cx="220.641" cy="339.531" r="4.54539" stroke="#E2BBBD" stroke-width="1.13635"/>
<circle cx="220.641" cy="339.531" r="3.40904" fill="#D5B1B1"/>
<g filter="url(#filter65_d_199_2731)">
<g filter="url(#filter66_ii_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M156.977 251.754C156.726 251.401 156.603 250.979 156.603 250.546V247.651C156.603 246.97 156.909 246.324 157.436 245.893L164.201 240.351C164.607 240.019 165.116 239.837 165.641 239.837H168.561C169.17 239.837 169.744 240.071 170.174 240.502C173.505 243.832 188.588 258.686 196.285 262.377C203.469 266.866 217.386 271.547 225.687 274.119C229.134 275.188 230.827 279.124 229.164 282.327L226.44 287.575C225.955 288.509 225.063 289.151 224.011 289.139C220.028 289.093 208.522 287.703 185.683 276.111C166.83 264.624 158.728 254.209 156.977 251.754Z" fill="url(#paint44_linear_199_2731)"/>
</g>
<g filter="url(#filter67_di_199_2731)">
<ellipse cx="166.442" cy="249.595" rx="3.25744" ry="3.25276" fill="#FCC432"/>
<path d="M168.976 249.595C168.976 250.991 167.842 252.125 166.442 252.125C165.041 252.125 163.907 250.991 163.907 249.595C163.907 248.199 165.041 247.066 166.442 247.066C167.842 247.066 168.976 248.199 168.976 249.595Z" stroke="#FCDA68" stroke-width="1.44644"/>
</g>
<g filter="url(#filter68_di_199_2731)">
<ellipse cx="219.285" cy="279.954" rx="3.25744" ry="3.25276" fill="#FCC432"/>
<path d="M221.819 279.954C221.819 281.35 220.685 282.484 219.285 282.484C217.884 282.484 216.751 281.35 216.751 279.954C216.751 278.558 217.884 277.425 219.285 277.425C220.685 277.425 221.819 278.558 221.819 279.954Z" stroke="#FCDA68" stroke-width="1.44644"/>
</g>
</g>
<g filter="url(#filter69_i_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M115.362 274.956C115.362 274.956 88.3295 263.797 79.6727 257.208C63.2106 246.732 64.2714 228.139 70.1402 220.564C84.4028 202.155 118.957 211.173 129.051 223.775C145.537 239.831 143.086 270.595 162.325 274.956C162.905 275.947 162.987 276.808 162.829 277.521C162.392 279.483 159.903 279.759 158.04 279.007C153.727 277.265 145.481 272.456 138.705 259.797C129.051 241.761 126.637 223.153 99.9042 220.564C78.8587 218.575 76.3133 232.496 76.3133 232.496C76.3133 232.496 75.5175 241.794 84.0898 248.574C101.563 258.847 117.115 263.498 120.468 264.56C120.378 265.526 119.101 266.83 117.319 270.095C116.666 271.291 115.362 274.956 115.362 274.956Z" fill="#FFFFFD"/>
</g>
<mask id="mask9_199_2731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="66" y="210" width="97" height="70">
<path fill-rule="evenodd" clip-rule="evenodd" d="M115.362 274.956C115.362 274.956 88.3295 263.797 79.6727 257.208C63.2106 246.732 64.2714 228.139 70.1402 220.564C84.4028 202.155 118.957 211.173 129.051 223.775C145.537 239.831 143.086 270.595 162.325 274.956C162.905 275.947 162.987 276.808 162.829 277.521C162.392 279.483 159.903 279.759 158.04 279.007C153.727 277.265 145.481 272.456 138.705 259.797C129.051 241.761 126.637 223.153 99.9042 220.564C78.8587 218.575 76.3133 232.496 76.3133 232.496C76.3133 232.496 75.5175 241.794 84.0898 248.574C101.563 258.847 117.115 263.498 120.468 264.56C120.378 265.526 119.101 266.83 117.319 270.095C116.666 271.291 115.362 274.956 115.362 274.956Z" fill="white"/>
</mask>
<g mask="url(#mask9_199_2731)">
<path opacity="0.212021" d="M154.498 269.473C154.498 269.473 157.393 278.537 154.498 279.593" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M148.318 260.799C148.318 260.799 150.108 267.633 143.639 270.196" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M142.192 245.62C142.192 245.62 141.101 252.828 135.677 253.571" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M133.485 228.995C133.485 228.995 133.887 236.78 126.266 237.669" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M118.395 216.706C118.395 216.706 120.684 221.426 114.684 225.38" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M98.0356 210.201C98.0356 210.201 99.9925 214.542 99.3044 220.321" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M73.4233 217.429C73.4233 217.429 78.8542 217.522 82.1099 222.489" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M66.1841 236.223C66.1841 236.223 71.3131 235.259 75.5945 235.581" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M74.146 251.403C74.146 251.403 80.4002 246.352 81.3848 245.62" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M87.6011 262.278C87.6011 262.278 89.9845 254.946 92.7303 253.166" stroke="#FFA231" stroke-width="0.723219"/>
<path opacity="0.212021" d="M100.629 268.788C100.629 268.788 102.674 260.86 106.157 259.117" stroke="#FFA231" stroke-width="0.723219"/>
</g>
<g filter="url(#filter70_di_199_2731)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M218.586 340.972C217.908 340.187 217.543 339.089 218.198 338.284C218.986 337.315 220.531 336.301 223.474 336.733C232.633 347.231 221.411 354.104 221.411 354.104L217.43 355.213C217.43 355.213 221.595 352.076 221.861 348.284C221.852 345.257 219.896 342.488 218.586 340.972Z" fill="#FDFBF6"/>
</g>
<g opacity="0.415528">
<path opacity="0.53385" d="M446.664 173.952C446.8 174.305 446.812 175.186 445.606 176.84C444.459 178.412 442.5 180.262 439.784 182.258C434.374 186.232 426.322 190.523 416.914 194.134C407.505 197.746 398.651 199.945 391.971 200.611C388.617 200.946 385.923 200.882 384.019 200.481C382.016 200.059 381.436 199.396 381.301 199.043C381.165 198.689 381.153 197.809 382.359 196.155C383.505 194.583 385.464 192.733 388.181 190.737C393.591 186.762 401.642 182.472 411.051 178.861C420.459 175.249 429.313 173.05 435.993 172.384C439.348 172.049 442.041 172.113 443.946 172.514C445.949 172.936 446.528 173.599 446.664 173.952Z" stroke="url(#paint45_linear_199_2731)" stroke-width="3.85206"/>
<ellipse cx="413.934" cy="186.329" rx="17.9765" ry="18.0565" fill="url(#paint46_linear_199_2731)"/>
<path d="M405.923 176.364C405.923 176.364 408.528 171.996 415.937 172.754" stroke="white" stroke-width="2.88905" stroke-linecap="round"/>
<path opacity="0.532425" fill-rule="evenodd" clip-rule="evenodd" d="M400.016 185.873C398.853 185.017 397.902 184.045 397.213 182.988L396.309 183.468C396.154 183.551 396 183.634 395.848 183.718L394.941 184.217L394.054 184.718L393.188 185.22L392.343 185.722L391.52 186.224C391.385 186.308 391.25 186.391 391.117 186.475L390.327 186.977C390.197 187.06 390.068 187.144 389.941 187.227L389.185 187.727L388.454 188.226L387.747 188.723L387.064 189.218L386.407 189.71L385.775 190.199C385.672 190.28 385.57 190.362 385.469 190.443L384.878 190.927C384.01 191.65 383.233 192.361 382.555 193.052L382.118 193.511C379.865 195.94 378.885 198.116 379.522 199.784C380.2 201.557 382.617 202.528 386.261 202.749L386.916 202.78L387.597 202.796L388.302 202.796L389.03 202.781C389.276 202.773 389.527 202.763 389.781 202.75L390.555 202.705C390.685 202.696 390.817 202.686 390.95 202.676L391.755 202.608L392.582 202.524C392.722 202.509 392.862 202.493 393.003 202.477L393.86 202.371C394.004 202.352 394.149 202.332 394.295 202.312L395.18 202.184L396.084 202.04L397.005 201.882L397.943 201.71L398.897 201.522L399.867 201.32L400.853 201.104L401.852 200.873L402.866 200.627L403.893 200.368L404.932 200.094L405.984 199.805C406.16 199.756 406.337 199.706 406.514 199.656L407.582 199.347L408.661 199.023L409.749 198.686L410.847 198.335L411.953 197.969L413.067 197.59L414.188 197.198C414.376 197.131 414.564 197.064 414.752 196.996L415.883 196.583L417.02 196.156L417.59 195.938L418.725 195.494C418.914 195.42 419.101 195.345 419.288 195.27L420.404 194.817L421.506 194.358L422.594 193.893L423.667 193.423L424.724 192.949L425.766 192.47L426.792 191.988L427.8 191.501L428.791 191.012C428.955 190.93 429.118 190.848 429.28 190.766L430.245 190.272L431.19 189.776L432.117 189.278C432.269 189.194 432.421 189.111 432.573 189.028L433.469 188.528L434.346 188.026L435.201 187.524L436.035 187.022C436.173 186.938 436.309 186.855 436.444 186.771L437.245 186.269L438.024 185.768L438.779 185.268C438.903 185.185 439.026 185.101 439.148 185.018L439.867 184.52C439.985 184.438 440.102 184.355 440.218 184.272L440.9 183.777L441.557 183.285C441.665 183.203 441.771 183.122 441.876 183.04L442.495 182.553C442.999 182.148 443.476 181.746 443.924 181.349L444.448 180.876C444.617 180.718 444.782 180.562 444.943 180.407L445.409 179.943C447.97 177.329 449.12 174.983 448.442 173.211C447.705 171.282 444.907 170.302 440.711 170.205L440.018 170.197C439.783 170.197 439.544 170.2 439.302 170.205L438.562 170.227C438.186 170.243 437.802 170.264 437.41 170.29L436.614 170.351C436.346 170.374 436.074 170.399 435.798 170.427L434.961 170.518L434.105 170.624L433.229 170.745C433.081 170.767 432.933 170.789 432.784 170.811L431.881 170.955C431.729 170.98 431.576 171.005 431.422 171.032L430.493 171.197C430.336 171.226 430.179 171.255 430.022 171.285L429.067 171.473L428.097 171.675L427.112 171.891L426.172 172.109C427.459 173.071 428.481 174.178 429.166 175.385C431.673 174.856 434.032 174.477 436.184 174.26C439.293 173.945 441.819 173.989 443.537 174.35L443.782 174.405C444.097 174.48 444.361 174.563 444.573 174.647L444.629 174.672L444.599 174.738C444.472 174.996 444.281 175.308 444.024 175.661C442.985 177.087 441.135 178.819 438.614 180.676C433.163 184.691 425.165 188.888 416.203 192.343C407.241 195.799 398.502 198.056 391.778 198.735C388.67 199.049 386.143 199.006 384.425 198.645L384.18 198.59C383.865 198.514 383.601 198.432 383.389 198.348L383.332 198.323L383.363 198.257C383.491 197.999 383.681 197.687 383.939 197.333C384.977 195.908 386.827 194.176 389.348 192.319C392.258 190.175 395.894 187.98 400.016 185.873Z" fill="url(#paint47_linear_199_2731)"/>
</g>
<path opacity="0.640183" d="M14.4802 318.94C13.9294 320.375 14.1278 323.205 17.6049 327.958C20.939 332.516 26.5824 337.818 34.3262 343.497C49.7592 354.815 72.6755 367.008 99.4119 377.271C126.148 387.535 151.337 393.807 170.379 395.723C179.934 396.684 187.676 396.52 193.204 395.364C198.968 394.158 201.009 392.188 201.56 390.753C202.11 389.318 201.912 386.489 198.435 381.736C195.101 377.178 189.457 371.876 181.714 366.197C166.281 354.878 143.364 342.685 116.628 332.422C89.8915 322.159 64.7025 315.886 45.6603 313.971C36.1056 313.009 28.3637 313.174 22.8362 314.33C17.0717 315.535 15.0309 317.505 14.4802 318.94Z" stroke="url(#paint48_linear_199_2731)" stroke-width="9.09078"/>
<circle r="49.4311" transform="matrix(-1 0 0 1 107.736 354.676)" fill="url(#paint49_linear_199_2731)" stroke="white" stroke-width="3.40904"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M149.317 351.913C151.866 349.774 153.972 347.421 155.553 344.905C189.072 362.42 210.042 381.34 205.803 392.382C200.15 407.111 151.787 402.245 97.7832 381.515C43.7791 360.785 4.58327 332.04 10.237 317.311C13.5813 308.599 31.8694 306.743 57.4293 310.971L59.3124 311.291L61.2208 311.633L63.1538 311.996L65.1108 312.381L67.091 312.787C67.423 312.857 67.7559 312.927 68.0897 312.999L70.1036 313.437L72.1391 313.897C72.3607 313.948 72.5827 313.999 72.805 314.051C69.6452 316.315 67.0165 318.871 65.0389 321.646C64.2594 321.483 63.4856 321.326 62.7178 321.174L60.901 320.821C60.2993 320.707 59.7014 320.596 59.1076 320.488L57.3381 320.175L55.5933 319.882C54.1498 319.646 52.7327 319.43 51.3438 319.235L49.6908 319.012C49.4176 318.976 49.1456 318.942 48.8747 318.908L47.2636 318.714C45.6668 318.531 44.1133 318.378 42.606 318.256L41.1143 318.143C37.6702 317.905 34.4837 317.834 31.5943 317.932C26.7828 318.096 23.0286 318.73 20.5641 319.697L20.3024 319.802C19.7117 320.049 19.2616 320.293 18.9462 320.513L18.8198 320.606L18.8539 320.767L18.905 320.961C19.2123 322.041 19.9889 323.517 21.2745 325.274C24.3447 329.471 29.7237 334.484 37.0153 339.832C52.6254 351.28 75.4654 363.21 101.042 373.028L102.259 373.493C127.409 383.049 151.88 389.294 170.835 391.201C179.832 392.106 187.184 391.98 192.274 390.915C194.405 390.469 195.97 389.892 196.921 389.295L197.108 389.172L197.223 389.089L197.208 389.013L197.164 388.834L197.069 388.512C196.724 387.455 195.967 386.06 194.767 384.419C191.697 380.222 186.318 375.209 179.027 369.862C170.88 363.887 160.764 357.781 149.317 351.913Z" fill="url(#paint50_linear_199_2731)"/>
<path d="M137.609 334.518C137.609 334.518 132.706 320.996 111.663 319.085" stroke="url(#paint51_linear_199_2731)" stroke-width="6.81808" stroke-linecap="round"/>
<circle cx="91.8282" cy="342.176" r="10.2271" fill="url(#paint52_linear_199_2731)"/>
<circle cx="414.55" cy="375.131" r="11.3635" stroke="url(#paint53_linear_199_2731)" stroke-width="4.54539"/>
<circle opacity="0.242528" cx="98.6464" cy="60.3627" r="13.6362" fill="url(#paint54_linear_199_2731)"/>
<circle opacity="0.746247" cx="402.051" cy="96.7257" r="7.95443" stroke="url(#paint55_linear_199_2731)" stroke-width="4.54539"/>
<g opacity="0.327687">
<rect x="80.4653" y="183.088" width="27.2723" height="6.81808" rx="3.40904" fill="url(#paint56_linear_199_2731)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M94.1012 172.861C95.984 172.861 97.5103 174.387 97.5103 176.27V196.724C97.5103 198.607 95.984 200.133 94.1012 200.133C92.2185 200.133 90.6922 198.607 90.6922 196.724V176.27C90.6922 174.387 92.2185 172.861 94.1012 172.861Z" fill="url(#paint57_linear_199_2731)"/>
</g>
<defs>
<filter id="filter0_f_199_2731" x="128.938" y="57.8677" width="293.176" height="293.177" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="14.2387" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter1_f_199_2731" x="131.903" y="263.073" width="372.721" height="372.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="22.5447" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter2_f_199_2731" x="333.181" y="130.841" width="230.174" height="230.173" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="22.5447" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter3_i_199_2731" x="129.5" y="185.088" width="38.8823" height="70.5712" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.97137 0 0 0 0 0.814862 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter4_ii_199_2731" x="173.67" y="326.712" width="38.803" height="36.7914" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.721578 0 0 0 0 0.368566 0 0 0 0 0.368566 0 0 0 0.98324 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.27269"/>
<feGaussianBlur stdDeviation="4.54539"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.258912 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter5_ii_199_2731" x="192.186" y="314.13" width="36.8404" height="47.4861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.75078 0 0 0 0 0.52958 0 0 0 0 0.542591 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.898039 0 0 0 0 0.690196 0 0 0 0 0.635294 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter6_i_199_2731" x="223.495" y="292.6" width="33.0969" height="28.2635" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.878431 0 0 0 0 0.572549 0 0 0 0 0.470588 0 0 0 0.285585 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter7_i_199_2731" x="208.188" y="300.735" width="40.1705" height="44.6517" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.956863 0 0 0 0 0.596078 0 0 0 0 0.027451 0 0 0 0.607672 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter8_ii_199_2731" x="213.74" y="299.702" width="33.4811" height="42.8904" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.952941 0 0 0 0 0.627451 0 0 0 0 0.129412 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter9_ii_199_2731" x="99.8467" y="304.133" width="79.1094" height="53.4962" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.40904"/>
<feGaussianBlur stdDeviation="2.84087"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.737255 0 0 0 0 0.152941 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="3.40904"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.564706 0 0 0 0 0.0470588 0 0 0 0.729268 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter10_iii_199_2731" x="115.09" y="254.372" width="30.5775" height="47.3048" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.980392 0 0 0 0 0.588235 0 0 0 0 0.0666667 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="9.09078" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.564706 0 0 0 0 0.0666667 0 0 0 0.581312 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_2731" result="effect3_innerShadow_199_2731"/>
</filter>
<filter id="filter11_i_199_2731" x="131.893" y="233.998" width="109.441" height="111.163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-6.81808"/>
<feGaussianBlur stdDeviation="7.38626"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.560784 0 0 0 0 0.0431373 0 0 0 0.398798 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter12_f_199_2731" x="148.187" y="221.233" width="99.8569" height="97.6721" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.6791" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter13_f_199_2731" x="159.039" y="201.543" width="90.2982" height="75.6954" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.37312" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter14_d_199_2731" x="116.698" y="230.973" width="120.157" height="133.495" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.281986 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter15_f_199_2731" x="138.719" y="242.448" width="91.411" height="75.7765" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.00815" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter16_iii_199_2731" x="116.698" y="247.769" width="109.485" height="112.154" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.815313 0 0 0 0 0.815313 0 0 0 0 0.815313 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.40904" dy="-1.13635"/>
<feGaussianBlur stdDeviation="8.5226"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.836039 0 0 0 0 0.822878 0 0 0 0 0.752079 0 0 0 0.775747 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_2731" result="effect3_innerShadow_199_2731"/>
</filter>
<filter id="filter17_f_199_2731" x="139.577" y="299.663" width="74.2855" height="74.2872" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.0407" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter18_f_199_2731" x="101.226" y="230.862" width="62.4168" height="62.4181" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.0407" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter19_f_199_2731" x="101.777" y="281.527" width="62.4168" height="62.4181" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.0407" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter20_f_199_2731" x="160.446" y="333.788" width="21.4045" height="14.9133" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.00407" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter21_f_199_2731" x="130.214" y="328.406" width="34.5568" height="26.498" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.00407" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter22_f_199_2731" x="131.306" y="244.08" width="102.473" height="102.486" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.0407" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter23_if_199_2731" x="130.847" y="268.327" width="45.8957" height="57.071" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feGaussianBlur stdDeviation="1.00407" result="effect2_foregroundBlur_199_2731"/>
</filter>
<filter id="filter24_diii_199_2731" x="142.844" y="261.206" width="60.2868" height="57.2678" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.819608 0 0 0 0.805743 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_2731" result="effect3_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.862467 0 0 0 0 0.862467 0 0 0 0 0.862467 0 0 0 0.436214 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_2731" result="effect4_innerShadow_199_2731"/>
</filter>
<filter id="filter25_d_199_2731" x="147.711" y="266.134" width="49.5813" height="46.7644" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.845666 0 0 0 0 0.845666 0 0 0 0 0.845666 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter26_d_199_2731" x="205.456" y="253.358" width="30.2632" height="45.4258" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.241201 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter27_ii_199_2731" x="206.592" y="253.358" width="27.9905" height="42.0167" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.323654 0 0 0 0 0.431539 0 0 0 0.686191 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.578362 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter28_d_199_2731" x="210.407" y="282.265" width="11.1741" height="11.178" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0 0 0 0 0 0 0 0 0 0.37791 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter29_i_199_2731" x="209.27" y="278.856" width="11.1741" height="12.3143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="-3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.588235 0 0 0 0 0.0588235 0 0 0 0.912775 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter30_f_199_2731" x="206.695" y="275.955" width="21.2576" height="21.26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.01629" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter31_d_199_2731" x="156.627" y="229.837" width="41.3232" height="36.9331" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.241201 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter32_ii_199_2731" x="157.764" y="229.837" width="39.0505" height="33.524" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.323654 0 0 0 0 0.431539 0 0 0 0.686191 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.578362 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter33_d_199_2731" x="161.707" y="250.356" width="11.175" height="11.1743" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0 0 0 0 0 0 0 0 0 0.37791 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter34_i_199_2731" x="160.571" y="246.947" width="11.175" height="12.3107" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="-3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.588235 0 0 0 0 0.0588235 0 0 0 0.912775 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter35_f_199_2731" x="156.45" y="242.859" width="21.258" height="21.2578" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.01629" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter36_f_199_2731" x="162.583" y="222.213" width="72.3548" height="58.6214" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.37312" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter37_ii_199_2731" x="168.327" y="60.064" width="74.467" height="97.7582" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="5.68174"/>
<feGaussianBlur stdDeviation="3.40904"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.945098 0 0 0 0 0.701961 0 0 0 0.897131 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.952591 0 0 0 0 0.68394 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter38_di_199_2731" x="170.049" y="70.2411" width="72.2712" height="87.7931" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="3.97722"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904639 0 0 0 0 0.347983 0 0 0 0 0 0 0 0 0.719446 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="5.68174"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.529412 0 0 0 0 0.00392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter39_ii_199_2731" x="310.68" y="128.038" width="70.5745" height="96.5436" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="5.68174"/>
<feGaussianBlur stdDeviation="3.40904"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.945098 0 0 0 0 0.701961 0 0 0 0.897131 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.952591 0 0 0 0 0.68394 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter40_di_199_2731" x="304.543" y="132.747" width="69.2311" height="86.8449" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="3.97722"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904639 0 0 0 0 0.347983 0 0 0 0 0 0 0 0 0.719446 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="5.68174"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.529412 0 0 0 0 0.00392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter41_ii_199_2731" x="142.153" y="80.8012" width="204.411" height="205.26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.40904"/>
<feGaussianBlur stdDeviation="3.97722"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980598 0 0 0 0 0.782269 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-12.4998"/>
<feGaussianBlur stdDeviation="7.38626"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.627451 0 0 0 0 0.0392157 0 0 0 0.641781 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter42_f_199_2731" x="190.385" y="90.9101" width="145.231" height="145.141" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.5082" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter43_ddii_199_2731" x="144.388" y="118.145" width="179.144" height="112.947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="2.84087"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.0509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.961984 0 0 0 0 0.524494 0 0 0 0 0.00423549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_2731" result="effect2_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="2.84087"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.771443 0 0 0 0 0.0546035 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-9.09078"/>
<feGaussianBlur stdDeviation="3.40904"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.384314 0 0 0 0 0.25098 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_2731" result="effect4_innerShadow_199_2731"/>
</filter>
<filter id="filter44_ddiii_199_2731" x="133.213" y="157.331" width="49.4403" height="49.4135" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.13635" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_199_2731"/>
<feOffset dx="2.27269" dy="3.40904"/>
<feGaussianBlur stdDeviation="3.97722"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576471 0 0 0 0 0.0470588 0 0 0 0.656998 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.560784 0 0 0 0 0.0431373 0 0 0 0.451622 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_2731" result="effect2_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6.81808" dy="5.68174"/>
<feGaussianBlur stdDeviation="4.54539"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.935122 0 0 0 0 0.669713 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="-3.40904"/>
<feGaussianBlur stdDeviation="4.54539"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.545098 0 0 0 0 0.0392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_2731" result="effect4_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.27269" dy="4.54539"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950004 0 0 0 0 0.723552 0 0 0 0.500597 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_199_2731" result="effect5_innerShadow_199_2731"/>
</filter>
<filter id="filter45_f_199_2731" x="173.806" y="107.356" width="123.731" height="123.66" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.5082" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter46_diii_199_2731" x="254.282" y="188.195" width="61.1806" height="61.1437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.13635" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_199_2731"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="3.97722"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576471 0 0 0 0 0.0470588 0 0 0 0.656998 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.68174"/>
<feGaussianBlur stdDeviation="4.54539"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.935122 0 0 0 0 0.669713 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.40904"/>
<feGaussianBlur stdDeviation="2.84087"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.545098 0 0 0 0 0.0392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_199_2731" result="effect3_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.54539"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950004 0 0 0 0 0.723552 0 0 0 0.500597 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_2731" result="effect4_innerShadow_199_2731"/>
</filter>
<filter id="filter47_ddii_199_2731" x="159.374" y="165.511" width="110.278" height="102.417" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.68174"/>
<feGaussianBlur stdDeviation="5.68174"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576132 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.584314 0 0 0 0 0.054902 0 0 0 0.825473 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_2731" result="effect2_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.13635" operator="erode" in="SourceAlpha" result="effect3_innerShadow_199_2731"/>
<feOffset dy="4.54539"/>
<feGaussianBlur stdDeviation="3.40904"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.943556 0 0 0 0 0.725479 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-7.95443"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.638854 0 0 0 0 0.0197456 0 0 0 0.11004 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_2731" result="effect4_innerShadow_199_2731"/>
</filter>
<filter id="filter48_f_199_2731" x="183.415" y="158.883" width="84.4656" height="84.42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.17304" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter49_d_199_2731" x="207.71" y="167.333" width="33.9956" height="28.1521" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.769066 0 0 0 0 0.621384 0 0 0 0 0.00850142 0 0 0 0.345285 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter50_i_199_2731" x="209.982" y="165.06" width="29.4502" height="25.8794" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="3.97722"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.297765 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter51_f_199_2731" x="208.782" y="160.731" width="35.4918" height="30.6185" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.67031" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter52_di_199_2731" x="198.625" y="106.859" width="18.9109" height="12.6635" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.411765 0 0 0 0 0.0235294 0 0 0 0.695526 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.262548 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter53_di_199_2731" x="288.009" y="134.824" width="16.0378" height="16.9775" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.411765 0 0 0 0 0.0235294 0 0 0 0.695526 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.262548 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter54_d_199_2731" x="247.894" y="152.403" width="40.5573" height="46.5649" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.27269" dy="3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.0509804 0 0 0 0.551709 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter55_i_199_2731" x="258.679" y="159.651" width="26.3824" height="30.8642" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="-2.27269"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.232984 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter56_d_199_2731" x="178.348" y="139.574" width="38.905" height="22.5483" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.175182 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter57_di_199_2731" x="194.792" y="188.42" width="37.7966" height="70.1417" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.929412 0 0 0 0 0.447059 0 0 0 0 0.113725 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.40904" dy="15.9089"/>
<feGaussianBlur stdDeviation="8.5226"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter58_i_199_2731" x="197.2" y="186.484" width="38.6064" height="23.7295" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.95443"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.21264 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter59_di_199_2731" x="179.34" y="218.523" width="53.265" height="34.7223" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.40904"/>
<feGaussianBlur stdDeviation="5.68174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.190816 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.40904" dy="-4.54539"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.0969255 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter60_f_199_2731" x="117.91" y="311.977" width="14.8672" height="35.6721" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.32666" result="effect1_foregroundBlur_199_2731"/>
</filter>
<filter id="filter61_i_199_2731" x="124.206" y="45.5892" width="271.631" height="259.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.27269"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.537255 0 0 0 0 0 0 0 0 0.503444 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter62_i_199_2731" x="133.495" y="57.0371" width="251.922" height="239.236" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.356934 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter63_ddii_199_2731" x="221.468" y="232.196" width="120.802" height="80.7067" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="-3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.894118 0 0 0 0 0.572549 0 0 0 0 0.0823529 0 0 0 0.20272 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.13635" dy="3.40904"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161898 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_199_2731" result="effect2_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3.40904" dy="-5.68174"/>
<feGaussianBlur stdDeviation="5.68174"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.584314 0 0 0 0 0.0862745 0 0 0 0.820524 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="3.40904"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_199_2731" result="effect4_innerShadow_199_2731"/>
</filter>
<filter id="filter64_i_199_2731" x="215.527" y="334.417" width="10.2275" height="13.6361" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.40904"/>
<feGaussianBlur stdDeviation="2.84087"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.761234 0 0 0 0 0.621159 0 0 0 0 0.621159 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter65_d_199_2731" x="154.33" y="239.837" width="77.7519" height="53.8471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.27269"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.308701 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
</filter>
<filter id="filter66_ii_199_2731" x="156.603" y="238.701" width="73.2065" height="51.5745" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.40904"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.607843 0 0 0 0 0.0823529 0 0 0 0 0.00784314 0 0 0 0.452076 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.40904"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.0901961 0 0 0 0 0.0392157 0 0 0 0.247561 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_199_2731" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter67_di_199_2731" x="160.911" y="244.07" width="8.78734" height="9.91466" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.937255 0 0 0 0 0.341176 0 0 0 0 0.32549 0 0 0 0.501426 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter68_di_199_2731" x="216.027" y="276.702" width="8.78734" height="8.77831" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.937255 0 0 0 0 0.341176 0 0 0 0 0.32549 0 0 0 0.501426 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.13635"/>
<feGaussianBlur stdDeviation="1.13635"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<filter id="filter69_i_199_2731" x="66.2192" y="206.7" width="96.6802" height="72.6922" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.40904"/>
<feGaussianBlur stdDeviation="2.27269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.772437 0 0 0 0 0.420035 0 0 0 0 0 0 0 0 0.527909 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_199_2731"/>
</filter>
<filter id="filter70_di_199_2731" x="215.157" y="335.494" width="13.0497" height="21.9913" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.13635" dy="1.13635"/>
<feGaussianBlur stdDeviation="0.568174"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.192834 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_199_2731"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_199_2731" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13635" dy="-1.13635"/>
<feGaussianBlur stdDeviation="1.70452"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.635294 0 0 0 0 0.188235 0 0 0 0.710903 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_199_2731"/>
</filter>
<linearGradient id="paint0_linear_199_2731" x1="56.6021" y1="55.8174" x2="56.6021" y2="401.267" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE900" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FFCE00"/>
</linearGradient>
<linearGradient id="paint1_linear_199_2731" x1="351.527" y1="351.187" x2="223.448" y2="302.171" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E460"/>
<stop offset="1" stop-color="#FFC028"/>
</linearGradient>
<linearGradient id="paint2_linear_199_2731" x1="397.04" y1="297.746" x2="337.3" y2="211.482" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#8F600C"/>
</linearGradient>
<linearGradient id="paint3_linear_199_2731" x1="290.64" y1="353.676" x2="297.771" y2="258.847" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#3D2700"/>
</linearGradient>
<linearGradient id="paint4_linear_199_2731" x1="214.386" y1="333.979" x2="240.723" y2="275.432" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#3D2700"/>
</linearGradient>
<linearGradient id="paint5_linear_199_2731" x1="149.7" y1="188.174" x2="180.47" y2="230.917" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD56A"/>
<stop offset="1" stop-color="#FF9E26"/>
</linearGradient>
<linearGradient id="paint6_linear_199_2731" x1="234.291" y1="355.31" x2="198.838" y2="311.381" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5A6A6"/>
<stop offset="1" stop-color="#D79796"/>
</linearGradient>
<linearGradient id="paint7_linear_199_2731" x1="203.221" y1="337.878" x2="218.949" y2="351.345" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7E6E6"/>
<stop offset="1" stop-color="#ECC8C8"/>
</linearGradient>
<linearGradient id="paint8_linear_199_2731" x1="247.048" y1="314.993" x2="257.563" y2="301.875" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCF3F3"/>
<stop offset="1" stop-color="#F7E2E2"/>
</linearGradient>
<linearGradient id="paint9_linear_199_2731" x1="257.45" y1="320.088" x2="227.861" y2="291.929" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FEF4CB"/>
</linearGradient>
<linearGradient id="paint10_linear_199_2731" x1="250.153" y1="311.934" x2="219.059" y2="294.9" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDE778"/>
<stop offset="1" stop-color="#F08500"/>
</linearGradient>
<linearGradient id="paint11_linear_199_2731" x1="82.6374" y1="310.221" x2="134.259" y2="280.042" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF2A7"/>
<stop offset="1" stop-color="#FDA61C"/>
</linearGradient>
<linearGradient id="paint12_linear_199_2731" x1="110.289" y1="267.577" x2="139.954" y2="236.1" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF4D"/>
<stop offset="1" stop-color="#FF9312"/>
</linearGradient>
<linearGradient id="paint13_linear_199_2731" x1="237.745" y1="272.417" x2="191.282" y2="347.173" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E669"/>
<stop offset="1" stop-color="#FFD73B"/>
</linearGradient>
<linearGradient id="paint14_linear_199_2731" x1="214.933" y1="319.749" x2="142.616" y2="359.746" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CFCAC1"/>
</linearGradient>
<linearGradient id="paint15_linear_199_2731" x1="171.019" y1="321.629" x2="158.651" y2="331.292" gradientUnits="userSpaceOnUse">
<stop stop-color="#EFEEEA" stop-opacity="0.01"/>
<stop offset="1" stop-color="#D0CEC6"/>
</linearGradient>
<linearGradient id="paint16_linear_199_2731" x1="188.578" y1="315.442" x2="163.115" y2="327.94" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E4E3E1"/>
</linearGradient>
<linearGradient id="paint17_linear_199_2731" x1="210.248" y1="298.249" x2="198.135" y2="286.602" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AA1E9"/>
<stop offset="1" stop-color="#3693F7"/>
</linearGradient>
<linearGradient id="paint18_linear_199_2731" x1="210.369" y1="302.291" x2="199.295" y2="294.051" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EBEBEB"/>
</linearGradient>
<linearGradient id="paint19_linear_199_2731" x1="0.226561" y1="6.71939" x2="4.76036" y2="11.1686" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE364"/>
<stop offset="1" stop-color="#FEBD27"/>
</linearGradient>
<linearGradient id="paint20_linear_199_2731" x1="157.643" y1="263.342" x2="172.028" y2="272.027" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AA1E9"/>
<stop offset="1" stop-color="#3693F7"/>
</linearGradient>
<linearGradient id="paint21_linear_199_2731" x1="153.708" y1="264.271" x2="164.533" y2="272.834" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EBEBEB"/>
</linearGradient>
<linearGradient id="paint22_linear_199_2731" x1="163.072" y1="257.074" x2="167.606" y2="261.523" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE364"/>
<stop offset="1" stop-color="#FEBD27"/>
</linearGradient>
<linearGradient id="paint23_linear_199_2731" x1="220.52" y1="68.2042" x2="218.84" y2="110.456" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF38"/>
<stop offset="1" stop-color="#FFB703"/>
</linearGradient>
<radialGradient id="paint24_radial_199_2731" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(201.958 93.6429) rotate(117.564) scale(40.7653 16.5921)">
<stop stop-color="#FFC102"/>
<stop offset="1" stop-color="#FF8D01"/>
</radialGradient>
<linearGradient id="paint25_linear_199_2731" x1="358.232" y1="123.844" x2="330.09" y2="155.404" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF38"/>
<stop offset="1" stop-color="#FFB703"/>
</linearGradient>
<radialGradient id="paint26_radial_199_2731" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(353.912 155.038) rotate(106.436) scale(40.7653 16.5921)">
<stop stop-color="#FFC102"/>
<stop offset="1" stop-color="#FF8D01"/>
</radialGradient>
<linearGradient id="paint27_linear_199_2731" x1="362.17" y1="151.568" x2="288.738" y2="302.869" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E669"/>
<stop offset="1" stop-color="#FFD73B"/>
</linearGradient>
<linearGradient id="paint28_linear_199_2731" x1="340.466" y1="162.491" x2="314.239" y2="229.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#AF8800"/>
<stop offset="1" stop-color="#604000"/>
</linearGradient>
<linearGradient id="paint29_linear_199_2731" x1="8.65669" y1="10.0359" x2="5.75732" y2="29.7681" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD048"/>
<stop offset="1" stop-color="#FF8C03"/>
</linearGradient>
<linearGradient id="paint30_linear_199_2731" x1="11.9079" y1="13.8052" x2="7.91964" y2="40.9483" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD048"/>
<stop offset="1" stop-color="#FF8C03"/>
</linearGradient>
<linearGradient id="paint31_linear_199_2731" x1="1.55427" y1="13.9973" x2="5.51282" y2="80.3706" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD435"/>
<stop offset="1" stop-color="#FFA718"/>
</linearGradient>
<linearGradient id="paint32_linear_199_2731" x1="237.641" y1="178.362" x2="230.957" y2="194.907" gradientUnits="userSpaceOnUse">
<stop stop-color="#815E00"/>
<stop offset="1" stop-color="#4A3000"/>
</linearGradient>
<linearGradient id="paint33_linear_199_2731" x1="269.641" y1="94.0641" x2="263.548" y2="102.65" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEF5B5"/>
<stop offset="1" stop-color="#FBEB76"/>
</linearGradient>
<linearGradient id="paint34_linear_199_2731" x1="215.068" y1="111.396" x2="211.631" y2="118.33" gradientUnits="userSpaceOnUse">
<stop stop-color="#B18104"/>
<stop offset="1" stop-color="#5E3B00"/>
</linearGradient>
<linearGradient id="paint35_linear_199_2731" x1="290.107" y1="136.105" x2="287.762" y2="143.48" gradientUnits="userSpaceOnUse">
<stop stop-color="#B18104"/>
<stop offset="1" stop-color="#5E3B00"/>
</linearGradient>
<linearGradient id="paint36_linear_199_2731" x1="254.314" y1="151.808" x2="254.314" y2="192.745" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DAD5C8"/>
</linearGradient>
<linearGradient id="paint37_linear_199_2731" x1="276.44" y1="158.04" x2="255.807" y2="171.386" gradientUnits="userSpaceOnUse">
<stop stop-color="#A37800"/>
<stop offset="1" stop-color="#6A4300"/>
</linearGradient>
<linearGradient id="paint38_linear_199_2731" x1="222.324" y1="220.464" x2="199.559" y2="242.181" gradientUnits="userSpaceOnUse">
<stop stop-color="#B34E51"/>
<stop offset="1" stop-color="#7C2628"/>
</linearGradient>
<linearGradient id="paint39_linear_199_2731" x1="217.961" y1="239.097" x2="214.52" y2="247.411" gradientUnits="userSpaceOnUse">
<stop stop-color="#F68080"/>
<stop offset="1" stop-color="#E53636"/>
</linearGradient>
<linearGradient id="paint40_linear_199_2731" x1="128.548" y1="339.593" x2="122.461" y2="340.324" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF4E" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FEA226"/>
</linearGradient>
<linearGradient id="paint41_linear_199_2731" x1="153.957" y1="131.216" x2="129.094" y2="142.731" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.529958" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint42_linear_199_2731" x1="353.245" y1="234.085" x2="359.962" y2="260.593" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.529958" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint43_linear_199_2731" x1="322.853" y1="309.419" x2="223.539" y2="318.017" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDE97C"/>
<stop offset="1" stop-color="#FBE167"/>
</linearGradient>
<linearGradient id="paint44_linear_199_2731" x1="124.947" y1="264.393" x2="170.998" y2="330.905" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCCB6"/>
<stop offset="0.215978" stop-color="#FDA783"/>
<stop offset="0.768626" stop-color="#F96E4C"/>
<stop offset="1" stop-color="#FF957B"/>
</linearGradient>
<linearGradient id="paint45_linear_199_2731" x1="444.84" y1="163.827" x2="375.881" y2="190.298" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6A6A"/>
<stop offset="1" stop-color="#FFCE00"/>
</linearGradient>
<linearGradient id="paint46_linear_199_2731" x1="396.032" y1="179.582" x2="405.346" y2="209.002" gradientUnits="userSpaceOnUse">
<stop stop-color="#94FFA2"/>
<stop offset="1" stop-color="#50A1FF"/>
</linearGradient>
<linearGradient id="paint47_linear_199_2731" x1="448.633" y1="170.197" x2="379.331" y2="170.197" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6A6A"/>
<stop offset="1" stop-color="#FFCE00"/>
</linearGradient>
<linearGradient id="paint48_linear_199_2731" x1="67.2407" y1="297.603" x2="85.3845" y2="414.524" gradientUnits="userSpaceOnUse">
<stop stop-color="#94FFA2" stop-opacity="0.36064"/>
<stop offset="1" stop-color="#50A1FF"/>
</linearGradient>
<linearGradient id="paint49_linear_199_2731" x1="65.7772" y1="118.79" x2="98.815" y2="54.0161" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9635A"/>
<stop offset="1" stop-color="#FBBC62"/>
</linearGradient>
<linearGradient id="paint50_linear_199_2731" x1="53.0232" y1="292.256" x2="162.932" y2="403.921" gradientUnits="userSpaceOnUse">
<stop stop-color="#94FFA2" stop-opacity="0.471401"/>
<stop offset="1" stop-color="#50A1FF"/>
</linearGradient>
<linearGradient id="paint51_linear_199_2731" x1="134.957" y1="315.812" x2="137.581" y2="334.484" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint52_linear_199_2731" x1="80.4133" y1="337.971" x2="83.7725" y2="354.083" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint53_linear_199_2731" x1="400.914" y1="361.495" x2="400.914" y2="388.767" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE900"/>
<stop offset="1" stop-color="#FFCE00"/>
</linearGradient>
<linearGradient id="paint54_linear_199_2731" x1="80.2976" y1="59.8967" x2="100.655" y2="75.2398" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F96B5B"/>
</linearGradient>
<linearGradient id="paint55_linear_199_2731" x1="391.837" y1="86.525" x2="391.837" y2="106.953" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE900" stop-opacity="0.297904"/>
<stop offset="1" stop-color="#FFCE00"/>
</linearGradient>
<linearGradient id="paint56_linear_199_2731" x1="107.738" y1="183.088" x2="80.4653" y2="183.088" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6A6A"/>
<stop offset="1" stop-color="#FFCE00"/>
</linearGradient>
<linearGradient id="paint57_linear_199_2731" x1="97.5103" y1="200.133" x2="97.5103" y2="172.861" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6A6A"/>
<stop offset="1" stop-color="#FFCE00" stop-opacity="0.187863"/>
</linearGradient>
</defs>
</svg>
