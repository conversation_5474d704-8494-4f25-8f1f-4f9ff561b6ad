<template>
  <div>
    <div v-if="!isMobile" class="download-container">
      <div class="content">
        <!-- 缤果数字教材 -->
        <div class="section">
          <div class="section-title">缤果数字教材 <span class="description">适用业务： 缤果数字教材</span></div>
          <div class="download-group">
            <div class="download-item" @mouseenter="mouseEnter('book_mac')" @mouseleave="mouseLeave('book_mac')">
              <template v-if="hoverState.book_mac">
                <a :href="downloads.book_mac" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/mac.png" alt="macOS" />
                  <div class="text">macOS</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('book_win')" @mouseleave="mouseLeave('book_win')">
              <template v-if="hoverState.book_win">
                <a :href="downloads.book_win" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/windows.png" alt="Windows" />
                  <div class="text">Windows</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('book_android')" @mouseleave="mouseLeave('book_android')">
              <template v-if="hoverState.book_android">
                <a :href="downloads.book_win" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/andriod_pc.png" alt="Android" />
                  <div class="text">Android/Pad</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('book_ios')" @mouseleave="mouseLeave('book_ios')">
              <template v-if="hoverState.book_ios">
                <a :href="downloads.book_win" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/ios_pc.png" alt="iOS" />
                  <div class="text">iOS/iPadOS</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mousemove="handleMouseMove($event, 'qrcode_bingo')">
              <div class="item-content">
                <img src="@/assets/images/download/qrcode_bingo.png" alt="公众号" class="qr-icon" />
                <div class="text">缤果数字教材 微信公众号</div>
              </div>
              <div class="qr-hover" :style="qrHoverStyle">
                <img src="@/assets/images/download/qrcode_bingo.png" alt="公众号" />
                <div class="text">扫码关注公众号</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 缤果空中课堂 -->
        <div class="section">
          <div class="section-title">缤果课堂 <span class="description">适用业务： 缤果空中课堂、缤果融合出版、缤果数字非遗</span></div>
          <div class="section-desc">学校上课端</div>
          <div class="download-group">
            <div class="download-item" @mouseenter="mouseEnter('live_mac')" @mouseleave="mouseLeave('live_mac')">
              <template v-if="hoverState.live_mac">
                <a :href="downloads.live_mac" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/mac.png" alt="macOS" />
                  <div class="text">macOS</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('live_win')" @mouseleave="mouseLeave('live_win')">
              <template v-if="hoverState.live_win">
                <a :href="downloads.live_win" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/windows.png" alt="Windows" />
                  <div class="text">Windows</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mousemove="handleMouseMove($event, 'qrcode_bingo')">
              <div class="item-content">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">缤果空中课堂 微信公众号</div>
              </div>
              <div class="qr-hover" :style="qrHoverStyle">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">扫码关注公众号</div>
              </div>
            </div>
          </div>
          <div class="section-desc">主讲老师端</div>
          <div class="download-group">
            <div class="download-item" @mouseenter="mouseEnter('live_mac_teacher')" @mouseleave="mouseLeave('live_mac_teacher')">
              <template v-if="hoverState.live_mac_teacher">
                <a :href="downloads.live_mac_teacher" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/mac.png" alt="macOS" />
                  <div class="text">macOS</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('live_win_teacher')" @mouseleave="mouseLeave('live_win_teacher')">
              <template v-if="hoverState.live_win_teacher">
                <a :href="downloads.live_win_teacher" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/windows.png" alt="Windows" />
                  <div class="text">Windows</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mousemove="handleMouseMove($event, 'qrcode_bingo')">
              <div class="item-content">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">缤果空中课堂 微信公众号</div>
              </div>
              <div class="qr-hover" :style="qrHoverStyle">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">扫码关注公众号</div>
              </div>
            </div>
          </div>
        </div>
        <div class="section">
          <div class="section-title">缤果课堂(人工智能版) <span class="description">适用业务：人工智能AI课</span></div>
          <div class="download-group">
            <div class="download-item" @mouseenter="mouseEnter('live_mac_aigc')" @mouseleave="mouseLeave('live_mac_aigc')">
              <template v-if="hoverState.live_mac_aigc">
                <a :href="downloads.live_mac_aigc" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/mac.png" alt="macOS" />
                  <div class="text">macOS</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('live_win_aigc')" @mouseleave="mouseLeave('live_win_aigc')">
              <template v-if="hoverState.live_win_aigc">
                <a :href="downloads.live_win_aigc" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/windows.png" alt="Windows" />
                  <div class="text">Windows</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mousemove="handleMouseMove($event, 'qrcode_bingo')">
              <div class="item-content">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">缤果空中课堂 微信公众号</div>
              </div>
              <div class="qr-hover" :style="qrHoverStyle">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">扫码关注公众号</div>
              </div>
            </div>
          </div>
        </div>
        <div class="section">
          <div class="section-title">缤果课堂(综合实践版) <span class="description">适用业务：综合实践AI课</span></div>
          <div class="download-group">
            <div class="download-item" @mouseenter="mouseEnter('live_mac_wenxuan')" @mouseleave="mouseLeave('live_mac_wenxuan')">
              <template v-if="hoverState.live_mac_wenxuan">
                <a :href="downloads.live_mac_wenxuan" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/mac.png" alt="macOS" />
                  <div class="text">macOS</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('live_win_wenxuan')" @mouseleave="mouseLeave('live_win_wenxuan')">
              <template v-if="hoverState.live_win_wenxuan">
                <a :href="downloads.live_win_wenxuan" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/windows.png" alt="Windows" />
                  <div class="text">Windows</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mousemove="handleMouseMove($event, 'qrcode_bingo')">
              <div class="item-content">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">缤果空中课堂 微信公众号</div>
              </div>
              <div class="qr-hover" :style="qrHoverStyle">
                <img src="@/assets/images/download/qr-code.png" alt="公众号" />
                <div class="text">扫码关注公众号</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 缤果学伴 -->
        <div class="section">
          <div class="section-title">缤果学伴</div>
          <div class="download-group">
            <div class="download-item" @mouseenter="mouseEnter('mate_android')" @mouseleave="mouseLeave('mate_android')">
              <template v-if="hoverState.mate_android">
                <a :href="downloads.mate_android" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/andriod_pc.png" alt="Android" />
                  <div class="text">Android/Pad</div>
                </div>
              </template>
            </div>

            <div class="download-item" @mouseenter="mouseEnter('mate_ios')" @mouseleave="mouseLeave('mate_ios')">
              <template v-if="hoverState.mate_ios">
                <a :href="downloads.mate_ios" target="_blank">
                  <div class="hover-icon">
                    <svg-icon icon-class="download" />
                  </div>
                  <div class="hover-text">点击下载</div>
                </a>
              </template>
              <template v-else>
                <div class="item-content">
                  <img src="@/assets/images/download/ios_pc.png" alt="iOS" />
                  <div class="text">iOS/iPadOS</div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端视图 -->
    <div v-else class="mobile-container">
      <!-- 缤果数字教材 -->
      <div class="mobile-section">
        <div class="mobile-title">缤果数字教材</div>
        <div class="mobile-downloads">
          <div class="download-list">
            <div class="download-btn" @click="mobileDownload(downloads.book_ios)">
              <img src="@/assets/images/download/ios.png" alt="iOS" />
              <span>iOS/iPadOS</span>
            </div>
            <div class="download-btn" @click="mobileDownload(downloads.book_android)">
              <img src="@/assets/images/download/andriod.png" alt="Android" />
              <span>Android/Pad</span>
            </div>
            <div class="qr-card">
              <img src="@/assets/images/download/qrcode_bingo.png" alt="公众号" class="qr-code" />
              <div class="qr-text">
                <p>缤果数字教材</p>
                <p>微信公众号</p>
              </div>
            </div>
            <div class="pc-tip">
              <div class="tip-text">
                <svg-icon icon-class="computer" class="pc-icon" />
                <span>PC端下载请访问：bingobook.cn</span>
              </div>
              <div class="copy-btn" @click="copyDomain('bingobook.cn')">复制</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 缤果融合出版 -->
      <div class="mobile-section">
        <div class="mobile-title">缤果融合出版</div>
        <div class="mobile-downloads">
          <div class="download-list">
            <div class="pc-tip">
              <div class="tip-text">
                <svg-icon icon-class="computer" class="pc-icon" />
                <span>PC端下载请访问：bingoclass.com.cn</span>
              </div>
              <div class="copy-btn" @click="copyDomain('bingoclass.com.cn')">复制</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 缤果数字非遗 -->
      <div class="mobile-section">
        <div class="mobile-title">缤果数字非遗</div>
        <div class="mobile-downloads">
          <div class="download-list">
            <div class="pc-tip">
              <div class="tip-text">
                <svg-icon icon-class="computer" class="pc-icon" />
                <span>PC端下载请访问：bingoclass.com.cn</span>
              </div>
              <div class="copy-btn" @click="copyDomain('bingoclass.com.cn')">复制</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 缤果空中课堂 -->
      <div class="mobile-section">
        <div class="mobile-title">缤果空中课堂</div>
        <!-- 学校上课端 -->
        <div class="sub-title">学校上课端</div>
        <div class="mobile-downloads">
          <div class="download-list">
            <div class="pc-tip">
              <div class="tip-text">
                <svg-icon icon-class="computer" class="pc-icon" />
                <span>PC端下载请访问：bingoclass.com.cn</span>
              </div>
              <div class="copy-btn" @click="copyDomain('bingoclass.com.cn')">复制</div>
            </div>
            <div class="qr-card">
              <img src="@/assets/images/download/qr-code.png" alt="公众号" class="qr-code" />
              <div class="qr-text">
                <p>缤果空中课堂</p>
                <p>微信公众号</p>
              </div>
            </div>
          </div>
        </div>
        <!-- 主讲老师端 -->
        <div class="sub-title">主讲老师端</div>
        <div class="mobile-downloads">
          <div class="download-list">
            <div class="pc-tip">
              <div class="tip-text">
                <svg-icon icon-class="computer" class="pc-icon" />
                <span>PC端下载请访问：bingoclass.com.cn</span>
              </div>
              <div class="copy-btn" @click="copyDomain('bingoclass.com.cn')">复制</div>
            </div>
            <div class="qr-card">
              <img src="@/assets/images/download/qr-code.png" alt="公众号" class="qr-code" />
              <div class="qr-text">
                <p>缤果空中课堂</p>
                <p>微信公众号</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 缤果学伴 -->
      <div class="mobile-section">
        <div class="mobile-title">缤果学伴</div>
        <div class="mobile-downloads">
          <div class="download-list">
            <div class="download-btn" @click="mobileDownload(downloads.mate_ios)">
              <img src="@/assets/images/download/ios.png" alt="iOS" />
              <span>iOS/iPadOS</span>
            </div>
            <div class="download-btn" @click="mobileDownload(downloads.mate_android)">
              <img src="@/assets/images/download/andriod.png" alt="Android" />
              <span>Android/Pad</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 微信打开提示层 -->
      <div v-if="showBrowserTip" class="browser-tip" @click="showBrowserTip = false">
        <i></i>
        <div>请在默认浏览器中打开</div>
      </div>
    </div>
  </div>
</template>

<script>
import { isWechat, isQQInstalled } from '@/utils/validate'
import { getDocumentTop } from '@/utils/index.js'
export default {
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  name: 'DownloadPage',
  data () {
    return {
      isMobile: false,
      showBrowserTip: false,
      hoverState: {},
      downloads: {
        book_mac: 'https://bingobook.cn/download',
        book_win: 'https://bingobook.cn/download',
        book_android: 'https://bingobook.cn/download',
        book_ios: 'https://bingobook.cn/download',
        plus_mac: 'https://bingoclass.com.cn/downloadstudent',
        plus_win: 'https://bingoclass.com.cn/downloadstudent',
        mate_mac: 'https://bingoclass.com.cn/downloadstudent',
        mate_win: 'https://bingoclass.com.cn/downloadstudent',
        live_mac: 'https://bingoclass.com.cn/downloadstudent',
        live_win: 'https://bingoclass.com.cn/downloadstudent',
        live_win_teacher: 'https://bingoclass.com.cn/downloadteacher',
        live_mac_teacher: 'https://bingoclass.com.cn/downloadteacher',
        mate_android: 'https://bingomate.com/download',
        mate_ios: 'https://bingomate.com/download',
        live_mac_aigc: 'https://bingoclass.com.cn/downloadaigc',
        live_win_aigc: 'https://bingoclass.com.cn/downloadaigc',
        live_mac_wenxuan: 'https://bingoclass.com.cn/downloadwenxuan',
        live_win_wenxuan: 'https://bingoclass.com.cn/downloadwenxuan'
        // ... 其他产品的下载链接
      },
      products: [
        {
          title: '缤果数字教材',
          domain: 'bingobook.cn',
          ios: '',
          android: ''
        }
        // ... 其他产品信息
      ],
      qrHoverStyle: {
        left: '0px',
        top: '0px'
      }
    }
  },
  created () {
    this.checkIsMobile()
    this.setNavFontState(0)
    this.setNavState(1)
    window.addEventListener('scroll', this.handleNavbarState)
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.handleNavbarState)
  },
  methods: {
    handleNavbarState () {
      const documentTop = getDocumentTop()
      if (documentTop < 10) {
        this.setNavState(1)
      } else {
        this.setNavState(0)
      }
    },
    checkIsMobile () {
      this.isMobile = document.body.clientWidth < 768
      window.onresize = () => {
        this.isMobile = document.body.clientWidth < 768
      }
    },
    mouseEnter (key) {
      if (this.isMobile) return
      this.$set(this.hoverState, key, true)
    },
    mouseLeave (key) {
      this.$set(this.hoverState, key, false)
    },
    mobileDownload (url) {
      const ua = navigator.userAgent.toLowerCase()
      if (isWechat(ua) || isQQInstalled(ua)) {
        this.showBrowserTip = true
        return
      }
      window.open(url)
    },
    copyDomain (domain) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(domain).then(() => {
          this.$message.success('复制成功')
        })
      } else {
        const textarea = document.createElement('textarea')
        textarea.value = domain
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
        this.$message.success('复制成功')
      }
    },
    handleMouseMove (event, type) {
      // 获取鼠标相对于视口的位置
      const mouseX = event.clientX
      const mouseY = event.clientY

      // 获取二维码浮层的尺寸
      const qrWidth = 240 // 200px图片 + 40px内边距
      const qrHeight = 260 // 200px图片 + 40px内边距 + 20px文字

      // 计算浮层位置，确保不超出视口
      let left = mouseX + 20 // 鼠标右侧20px处
      let top = mouseY - qrHeight / 2 // 垂直居中

      // 防止超出右边界
      if (left + qrWidth > window.innerWidth) {
        left = mouseX - qrWidth - 20 // 改为显示在鼠标左侧
      }

      // 防止超出上下边界
      if (top < 0) {
        top = 0
      } else if (top + qrHeight > window.innerHeight) {
        top = window.innerHeight - qrHeight
      }

      // 更新浮层位置
      this.qrHoverStyle = {
        left: `${left}px`,
        top: `${top}px`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {
  .download-container {
    min-height: 100vh;
    background: #E4EEFE;
    padding: 20px 0;
    padding-top: 80px;

    .content {
      width: 52%;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 16px;
      padding: 25px;

      .section-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 25px;
        text-align: left;
        .description{
          font-size: 12px;
          color: #666;
          margin-left: 40px;
        }
      }

      .section-desc {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        text-align: left;
        margin-top: 15px;
      }
      .download-group {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
      }

      .download-item {
        min-width: 220px;
        height: 60px;
        background: #fff;
        border-radius: 10px;
        display: flex;
        align-items: center;
        padding: 0 20px;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;

        &:hover {
          box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .item-content {
          display: flex;
          align-items: center;
          width: 100%;

          img {
            width: 30px;
            height: 30px;
            margin-right: 15px;
            margin-bottom: 0;

            &.qr-icon {
              width: 32px;
              height: 32px;
              object-fit: contain;
            }
        }

        .text {
          font-size: 14px;
          color: #333;
            white-space: nowrap;
            font-weight: 500;
          }
        }

        a {
          display: flex;
          align-items: center;
          width: 100%;
          text-decoration: none;

        .hover-icon {
            width: 30px;
            height: 30px;
          background: #2D9CDB;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
            margin-right: 15px;

          svg {
              width: 16px;
              height: 16px;
            color: #fff;
          }
        }

        .hover-text {
          font-size: 14px;
          color: #333;
            font-weight: 500;
          }
        }

        &.qr-item {
          width: 220px;
          height: auto;
          padding: 20px;
          img {
            width: 150px !important;
            height: 150px !important;
            margin: 0 auto 12px;
            display: block;
          }

          .text {
            text-align: center;
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }
        }

        .qr-hover {
          position: fixed;
          background: #fff;
          padding: 15px;
          border-radius: 12px;
          box-shadow: 0 0 20px rgba(0,0,0,0.15);
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s ease;
          z-index: 1000;
          pointer-events: none;

          img {
            width: 160px;
            height: 160px;
            display: block;
            margin-bottom: 8px;
          }

          .text {
            text-align: center;
            font-size: 12px;
            color: #666;
            white-space: nowrap;
          }
        }

        &:hover {
          .qr-hover {
            opacity: 1;
            visibility: visible;
          }
        }

        .item-content {
          .qr-icon {
            width: 30px;
            height: 30px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .mobile-container {
    min-height: 100vh;
    background: #f5f5f5;
    padding: pw(20) pw(15);

    .mobile-section {
      margin-bottom: pw(30);
      background: #fff;
      border-radius: pw(16);
      padding: pw(20);

      .mobile-title {
        font-size: pw(24);
        font-weight: bold;
        margin-bottom: pw(20);
        color: #333;
      }

      .sub-title {
        font-size: pw(18);
        font-weight: 500;
        margin: pw(20) 0 pw(15);
        color: #666;
      }

      .mobile-downloads {
        .download-list {
          display: flex;
          flex-direction: column;
          gap: pw(15);

          .download-btn {
            width: 100%;
            height: pw(60);
            background: linear-gradient(90deg, #209CFF 0%, #68E0CF 100%);
            border-radius: pw(10);
          display: flex;
          align-items: center;
            padding: 0 pw(20);

            img {
              width: pw(30);
              height: pw(30);
              margin-right: pw(15);
          }

          span {
            color: #fff;
              font-size: pw(16);
              font-weight: 500;
            }
          }

          .qr-card {
            background: #F8F9FC;
            border-radius: pw(10);
            padding: pw(15);
            display: flex;
            align-items: center;
            gap: pw(15);

            .qr-code {
              width: pw(80);
              height: pw(80);
              border-radius: pw(6);
            }

            .qr-text {
              flex: 1;

              p {
                font-size: pw(14);
                color: #333;
                line-height: 1.4;

                &:last-child {
                  color: #666;
                  font-size: pw(12);
                  margin-top: pw(4);
                }
              }
          }
        }

        .pc-tip {
          display: flex;
          align-items: center;
          justify-content: space-between;
            background: #F8F9FC;
            padding: pw(12) pw(15);
            border-radius: pw(8);

            .tip-text {
              display: flex;
              align-items: center;
              gap: pw(8);
              color: #666;
              font-size: pw(13);

              .pc-icon {
                width: pw(16);
                height: pw(16);
                color: #666;
              }
            }

          .copy-btn {
              padding: pw(6) pw(12);
            background: #E6F1FF;
              color: #2D9CDB;
              border-radius: pw(4);
              font-size: pw(12);
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .browser-tip {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    i {
      width: pw(94);
      height: pw(151);
      background: url('~@/assets/images/download/tip.png') no-repeat;
      background-size: contain;
      position: absolute;
      right: pw(20);
      top: pw(20);
    }

    div {
      color: #fff;
      font-size: pw(18);
      text-align: center;
      margin-top: pw(200);
    }
  }
}
</style>
