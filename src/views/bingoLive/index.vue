<template>
  <div class="w">
    <div class="bg-color-box">
      <div class="banner-title-box">
        <div class="banner-title">
          <div class="banner-title-weight animate__animated animate__fadeIn">
            <div v-if="showTitle" class="banner-title-weight mr15 text-align">缤果空中课堂</div>
            <div class="banner-title-weight mr15">科技让教育更平等</div>
          </div>
          <div class="banner-title-item animate__animated animate__fadeIn animate__delay-100">为每个孩子提供创造和实现他们美好梦想的机会</div>
        </div>
        <svg-icon icon-class="sky-ip" class="sky-img animate__animated animate__fadeIn animate__delay-100" />
      </div>

      <div class="banner-nav animate__animated animate__fadeIn animate__delay-200">
        <div class="item-box">
          <div class="item-title">
            严选
          </div>
          <div class="item-desc">
            优质师资
          </div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">
            互动式
          </div>
          <div class="item-desc">
            教学内容
          </div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">
            浸入式
          </div>
          <div class="item-desc">
            教学模式
          </div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">
            云保护
          </div>
          <div class="item-desc">
            数据安全
          </div>
        </div>
      </div>
    </div>

    <div class="content computer">
      <div class="pt100 pb100 flex justify-center">
        <div class="flex flex-col tl align-start w400 animate__animated animate__fadeInLeft">
          <svg-icon icon-class="sky-2" class="w230 h80 mb40" />
          <svg-icon icon-class="sky-1" class="w360 h120" />
          <div class="f16 lh30 fb color333 tl mt30">
            严选优质师资线上实景实时授课。<br />
            助教线下同步协作，减负备课授课。
          </div>
        </div>
        <div class="ml100 animate__animated animate__fadeIn animate__delay-300">
          <img class="w500 h300" src="../../assets/images/sky/sky-3.png" />
        </div>
      </div>
      <div class="flex flex-col align-center">
        <div class="w1000 flex justify-end">
          <div class="relative inline-block">
            <img width="350" height="180" src="../../assets/images/sky/sky-bg1.png" />
            <div v-animate-onscroll="'animate__animated animate__fadeIn'" class="absolute flex justify-center align-center items-center">
              <svg-icon class="h40 w330" icon-class="sky-duoduan" />
            </div>
          </div>
        </div>
        <div class="w1000 flex justify-between mb100">
          <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-300'" class="w30p relative img-hover">
            <img class="w" src="../../assets/images/sky/sky-4.png" />
            <div class="absolute flex justify-center align-center items-center color-white f35 fb">
              教学内容
            </div>
          </div>
          <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-400'" class="w30p relative img-hover">
            <img class="w" src="../../assets/images/sky/sky-5.png" />
            <div class="absolute flex justify-center align-center items-center color-white f35 fb">
              教学师资
            </div>
          </div>
          <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-500'" class="w30p relative img-hover">
            <img class="w" src="../../assets/images/sky/sky-6.png" />
            <div class="absolute flex justify-center align-center items-center color-white f35 fb">
              教学方法
            </div>
          </div>
        </div>

        <div class="w1000 flex justify-center">
          <svg-icon class="w500 h60" icon-class="sky-more" />
        </div>

        <div class="w1000 mb1 flex justify-around">
          <div class="tag-item" :class="tagActiveIndex === 0 ? 'tag-item-active' : ''" @mouseover="tagActiveIndex = 0">
            学校端
          </div>

          <div class="tag-item" :class="tagActiveIndex === 1 ? 'tag-item-active' : ''" @mouseover="tagActiveIndex = 1">
            教师端
          </div>

          <div class="tag-item" :class="tagActiveIndex === 2 ? 'tag-item-active' : ''" @mouseover="tagActiveIndex = 2">
            助教端
          </div>
        </div>
        <div class="flex justify-center">
          <div class="w1000 flex justify-center">
            <img v-show="tagActiveIndex === 0" class="w h500 object-contain" src="../../assets/images/sky/sky-7.png" />
            <img v-show="tagActiveIndex === 1" class="w h500 object-contain" src="../../assets/images/sky/sky-8.png" />
            <img v-show="tagActiveIndex === 2" class="w h500 object-contain" src="../../assets/images/sky/sky-9.png" />
          </div>
        </div>

        <div class="flex justify-center">
          <div class="w1000 flex justify-center mt100 mb60">
            <svg-icon class="w630 h120" icon-class="sky-3" />
          </div>
        </div>

        <div class="flex justify-center">
          <div class="w1000 flex justify-center mb30" @mouseleave="hoverImg = ''">
            <img class="mr20 pointer transition-all" :width="hoverImg === 'ctwh' ? 284 : 216" height="216" :src="hoverImg === 'ctwh' ? Group1 : ctwh" @mouseover="imgHover('ctwh')" />
            <img class="mr20 pointer transition-all" :width="hoverImg === 'aqjy' ? 284 : 216" height="216" :src="hoverImg === 'aqjy' ? Group4 : aqjy" @mouseover="imgHover('aqjy')" />
          </div>
        </div>

        <div class="flex justify-center">
          <div class="w1000 flex justify-center mb100" @mouseleave="hoverImg = ''">
            <img class="mr20 pointer transition-all" :width="hoverImg === 'fnkx' ? 284 : 216" height="216" :src="hoverImg === 'fnkx' ? Group6 : fnkx" @mouseover="imgHover('fnkx')" />
            <img class="mr20 pointer transition-all" :width="hoverImg === 'whcc' ? 284 : 216" height="216" :src="hoverImg === 'whcc' ? Group7 : whcc" @mouseover="imgHover('whcc')" />
          </div>
        </div>

        <div class="flex justify-center">
          <div class="w1000 flex justify-center align-center mb50">
            <svg-icon class="w40 h14" icon-class="sky-right" />
            <div class="ml20 mr20 f35">助力教育资源均衡发展</div>
            <svg-icon class="w40 h14" icon-class="sky-left" />
          </div>
        </div>

        <div class="w mb100">
          <swiper class="swiper slinter" :options="swiperOption">
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-1.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-2.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-3.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-4.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-5.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-6.png" />
            </swiper-slide>
          </swiper>
        </div>
      </div>
    </div>

    <div class="content h-5">
      <div class="pt20 pb30 flex flex-col align-center w">
        <div class="flex flex-col tc align-center w">
          <svg-icon icon-class="sky-2" class="w130 h44 mb20" />
          <svg-icon icon-class="sky-m-1" class="w200 h56" />
          <img class="h240 mt20 mb20" style="width: 95%;" src="../../assets/images/sky/sky-3.png" />
          <div class="f14 lh25 color333 tc">
            严选优质师资线上实景实时授课。<br />
            助教线下同步协作，减负备课授课。
          </div>
        </div>
      </div>

      <div class="w flex flex-col align-center">
        <div class="w tc pb10">
          <svg-icon class="h36 w170" icon-class="sky-duoduan" />
        </div>

        <div class="w pl10 pr10 box-border flex justify-between mb40">
          <el-row :gutter="10">
            <el-col :span="8">
              <div class="w relative">
                <img class="w" src="../../assets/images/sky/sky-4.png" />
                <div class="absolute flex justify-center align-center items-center color-white f16 fb">
                  教学内容
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="w relative">
                <img class="w" src="../../assets/images/sky/sky-5.png" />
                <div class="absolute flex justify-center align-center items-center color-white f16 fb">
                  教学师资
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="w relative">
                <img class="w" src="../../assets/images/sky/sky-6.png" />
                <div class="absolute flex justify-center align-center items-center color-white f16 fb">
                  教学方法
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="w flex justify-center pb20">
          <svg-icon class="w220 h30" icon-class="sky-more" />
        </div>

        <div class="w pl10 pr10 mb10 box-border flex justify-around">
          <div class="tag-item" :class="tagActiveIndex === 0 ? 'tag-item-active' : ''" @click="tagActiveIndex = 0" @mouseover="tagActiveIndex = 0">
            学校端
          </div>

          <div class="tag-item" :class="tagActiveIndex === 1 ? 'tag-item-active' : ''" @click="tagActiveIndex = 1" @mouseover="tagActiveIndex = 1">
            教师端
          </div>

          <div class="tag-item" :class="tagActiveIndex === 2 ? 'tag-item-active' : ''" @click="tagActiveIndex = 2" @mouseover="tagActiveIndex = 2">
            助教端
          </div>
        </div>
        <div class="w flex justify-center pl10 pr10 box-border">
          <div class="w flex justify-center">
            <img v-show="tagActiveIndex === 0" class="w h200" src="../../assets/images/sky/sky-m-7.jpg" />
            <img v-show="tagActiveIndex === 1" class="w h200" src="../../assets/images/sky/sky-m-8.jpg" />
            <img v-show="tagActiveIndex === 2" class="w h200" src="../../assets/images/sky/sky-m-9.jpg" />
          </div>
        </div>

        <div class="flex justify-center">
          <div class="w flex justify-center mt50 mb20">
            <svg-icon class="w280 h60" icon-class="sky-3" />
          </div>
        </div>
        <div class="w pl10 pr10 box-border">
          <el-row :gutter="10">
            <el-col :span="12">
              <img class="w mb10" :src="Group1" />
            </el-col>
            <el-col :span="12">
              <img class="w mb10" :src="Group3" />
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <img class="w mb10" :src="Group6" />
            </el-col>
            <el-col :span="12">
              <img class="w mb10" :src="Group7" />
            </el-col>
          </el-row>
          <!-- <el-row :gutter="10">
            <el-col :span="12">
              <img class="w mb10" :src="Group5" />
            </el-col>
            <el-col :span="12">
              <img class="w mb10" :src="Group6" />
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <img class="w" :src="Group7" />
            </el-col>
            <el-col :span="12">
              <img class="w" :src="Group8" />
            </el-col>
          </el-row> -->
        </div>

        <div class="flex justify-center">
          <div class="w flex justify-center align-center mb30 mt60">
            <div class="tc f20">助力教育资源均衡发展</div>
          </div>
        </div>

        <div class="w mb100">
          <swiper class="swiper slinter" :options="swiperOptionM">
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-1.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-2.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-3.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-4.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-5.png" />
            </swiper-slide>
            <swiper-slide>
              <img class="w h" src="../../assets/images/sky/sky-s-6.png" />
            </swiper-slide>
          </swiper>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ctwh from '@/assets/images/sky/ctwh.png'
import zhys from '@/assets/images/sky/zhys.png'
import kxsy from '@/assets/images/sky/kxsy.png'
import aqjy from '@/assets/images/sky/aqjy.png'
import mzfz from '@/assets/images/sky/mzfz.png'
import fnkx from '@/assets/images/sky/fnkx.png'
import whcc from '@/assets/images/sky/whcc.png'
import ldsj from '@/assets/images/sky/ldsj.png'

import Group1 from '@/assets/images/sky/Group1.png'
import Group2 from '@/assets/images/sky/Group2.png'
import Group3 from '@/assets/images/sky/Group3.png'
import Group4 from '@/assets/images/sky/Group4.png'
import Group5 from '@/assets/images/sky/Group5.png'
import Group6 from '@/assets/images/sky/Group6.png'
import Group7 from '@/assets/images/sky/Group7.png'
import Group8 from '@/assets/images/sky/Group8.png'
import { getDocumentTop } from '@/utils/index.js'
export default {
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  data () {
    return {
      showTitle: window.location.host !== 'bingoclass.com.cn',
      tagActiveIndex: 0,
      hoverImg: '',
      ctwh,
      zhys,
      kxsy,
      aqjy,
      mzfz,
      fnkx,
      whcc,
      ldsj,
      Group1,
      Group2,
      Group3,
      Group4,
      Group5,
      Group6,
      Group7,
      Group8,
      // 走马灯效果
      swiperOption: {
        loop: true,
        freeMode: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false
        },
        speed: 6000,
        spaceBetween: 35,
        // slidesPerGroup: 1,
        slidesPerView: 3
      },
      // 走马灯效果
      swiperOptionM: {
        loop: true,
        freeMode: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false
        },
        speed: 6000,
        spaceBetween: 10,
        slidesPerView: 2
      }
    }
  },
  mounted () {
    this.setFooterState(true)
    this.setNavState(1)
    this.setNavFontState(0)
    window.addEventListener('scroll', this.handleNavbarState)
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.handleNavbarState)
    this.setNavState(1)
  },
  methods: {
    imgHover (val) {
      this.hoverImg = val
    },
    handleNavbarState () {
      const documentTop = getDocumentTop()
      if (documentTop < 10) {
        this.setNavState(1)
      } else {
        this.setNavState(0)
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
  .bg-color-box {
    background: linear-gradient(90deg, #5398FF 0%, #3EB8FF 25%, #0BDEF1 50%, #71FFC6 75%, #D4FF75 100%);
  }
  @media screen and (min-width: 769px) {
    .transition-all {
      transition: all .3s linear;
    }
    .text-align{
        text-align: left;
    }
    .h-5 {
      display: none;
    }
    .h500 {
      height: 500px;
    }

    .sky-img {
      width: 361px;
      height: 361px;
    }

    .content {
      width: 100%;

      .color333 {
        color: #333333;
      }
      .color-white {
        color: #fff;
      }
      .f35 {
        font-size: 35px;
      }

      .mb250 {
        margin-bottom: 250px;
      }

      .mb200 {
        margin-bottom: 200px;
      }

      .items-center {
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
      }

      .w30p {
        width: 30%;
      }

      .tag-item {
        width: 100px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        box-sizing: border-box;
        cursor: pointer;
        color: #6C6868;
        &:hover {
          color: #6C83FF;
          border-bottom: 3px solid #6C83FF;
        }
      }

      .tag-item-active {
        color: #6C83FF !important;
        border-bottom: 3px solid #6C83FF;
      }
    }

    .img-hover {
      overflow: hidden;
      img {
        transition: all .3s linear;
      }
      &:hover {
        img {
          transform: scale(1.3);
        }
      }
    }

    .swiper {
      width: 100%;
      height: vw(370);
      position: relative;

      .swiper-slide {
        width: vw(500);
        height: vw(350);
        img {
          border-radius: 25px;
        }
      }
    }
  }
  @media screen and (max-width: 768px) {
    .text-align{
        text-align: center;
    }
    .computer {
      display: none;
    }

    .h240 {
      height: pw(240);
    }

    .sky-img {
      width: pw(177);
      height: pw(177);
    }

    .content {
      width: 100%;

      .color333 {
        color: #333333;
      }
      .color-white {
        color: #fff;
      }

      .items-center {
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
      }

      .w30p {
        width: 32%;
      }

      .w50p {
        width: 47%;
      }

      .tag-item {
        width: pw(40);
        // height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: pw(12);
        box-sizing: border-box;
        cursor: pointer;
        color: #6C6868;
        &:hover {
          color: #6C83FF;
          border-bottom: 3px solid #6C83FF;
        }
      }

      .tag-item-active {
        color: #6C83FF !important;
        border-bottom: 3px solid #6C83FF;
      }
    }
    .swiper {
      width: 100%;
      height: pw(180);
      position: relative;

      .swiper-slide {
        width: 100%;
        height: pw(150);
        img {
          border-radius: 25px;
        }
      }
    }
  }

  </style>
