<template>
  <div class="product-container">
    <div v-for="item in productList" :key="item.id" v-show="tabIndex === item.id" class="content-wrapper" :class="item.bgClass">
      <!-- 左侧文字内容 -->
      <div class="left-content">
        <h1 class="title animate__animated animate__fadeInDown">
          <div>{{ item.title }}</div>
          <div class="en-title">{{ item.enTitle }}</div>
        </h1>
        <div class="description animate__animated animate__delay-200 animate__fadeInDown">{{ item.description }}</div>
        <p class="detail-text animate__animated animate__delay-300 animate__fadeInLeft">
          {{ item.detailText }}
        </p>
        <div class="more-info animate__animated animate__delay-400 animate__fadeInUp">
          <a v-if="item.id === 4||item.id === 5" :href="item.link" class="link" target="_blank">查看详情</a>
          <a v-else  class="link" @click="handleCheckItemLink(item.link)">查看详情</a>
        </div>
      </div>

      <div class="circle1 animate__animated animate__fadeIn"></div>
      <div class="circle2 animate__animated animate__fadeIn"></div>

      <!-- 右侧图片区域 -->
      <div class="right-content animate__animated animate__fadeInRightBig">
        <img :src="item.image" :alt="item.title" class="scene-image" />
      </div>
    </div>

    <div class="check-group">
      <div
        v-for="item in productList"
        :key="item.id"
        class="check-item"
        :class="{ active: tabIndex === item.id }"
        @click="handleCheckItem(item.id)"
      >
        <img :src="item.checkIcon" :alt="item.title" />
      </div>
    </div>
  </div>
</template>

<script>
import { getDocumentTop } from '@/utils/index.js'

export default {
  name: 'ProductIndex',
  inject: ['setNavState', 'setNavFontState', 'setFooterState'],
  data () {
    return {
      isFontTransparent: false,
      tabIndex: 1,
      productList: [
        {
          id: 1,
          title: '缤果空中课堂',
          enTitle: 'BingoLive',
          description: '科技让教育更平等',
          detailText: '根据权威特色的素质教育课程与专业师资，采用1V1双师教学模式，通过直播视频技术配合多媒体互动课件动态展示，从教学内容、教学师资和教学方法三个方面保障空中课堂教学效果，赋能学校，促进优质资源均衡。',
          link: '/bingolive',
          image: require('@/assets/images/pruduct/detail1.png'),
          checkIcon: require('@/assets/images/pruduct/check1.png'),
          bgClass: 'bg-gradient-to-1'
        },
        {
          id: 2,
          title: '缤果融合出版',
          enTitle: 'BingoPlus',
          description: '科技让知识触达更高效',
          detailText: '依托纸质出版物，以人工智能为核心，利用科技手段将AIGC与虚拟学习助手（数字人）有机结合，融合优质内容、专业师资和创新教学方法，为校内外用户提供更优质的服务，有效解决学校专业师资不足开课难、教师负担重、图书应用率不够等问题。',
          link: '/bingoplus',
          image: require('@/assets/images/pruduct/detail2.png'),
          checkIcon: require('@/assets/images/pruduct/check2.png'),
          bgClass: 'bg-gradient-to-2'
        },
        {
          id: 3,
          title: '缤果数字非遗',
          enTitle: 'BingoRoots',
          description: '科技让文化活起来',
          detailText: '联合中国传统文化促进会、巴蜀文化数字研究院等学术机构，以非遗传统文化为依托，带领学习者在读名著的过程中领略中华文化之美，在动手制作传统饰物的过程中，感受、学习传统文化、非遗文化。以独特有趣的视角展现中国传统文化，集趣味、美学和历史文化于一体。',
          link: '/bingomate',
          image: require('@/assets/images/pruduct/detail3.png'),
          checkIcon: require('@/assets/images/pruduct/check3.png'),
          bgClass: 'bg-gradient-to-3'
        },
        {
          id: 4,
          title: '缤果数字教材',
          enTitle: 'BingoBook',
          description: '科技让知识更易得',
          detailText: '新形态AI互动式教材，支持多种阅读模式。围绕“支撑教学、优化学习、提升实践、高效创作、智慧出版”五大目标，融合先进的人工智能技术，助力技能提升与知识拓展，让教学更简单，让学习更高效。',
          link: 'https://bingobook.cn/#/home',
          image: require('@/assets/images/pruduct/detail4.png'),
          checkIcon: require('@/assets/images/pruduct/check4.png'),
          bgClass: 'bg-gradient-to-4'
        },
        {
          id: 5,
          title: '缤果学伴',
          enTitle: 'BingoMate',
          description: 'BingoMate',
          detailText: '专为教育打造的具身智能机器人，结合丰富的内容资源，并依托超级教师大模型和缤果平台的支撑。它不仅具备智能课堂教学能力，赋能新形态教学模式，推动教育创新升级。还能为家庭提供沉浸式学习陪伴，助力个性化成长。',
          link: 'https://bingomate.com',
          image: require('@/assets/images/pruduct/detail5.png'),
          checkIcon: require('@/assets/images/pruduct/check5.png'),
          bgClass: 'bg-gradient-to-5'
        }
      ]
    }
  },
  watch: {
    '$route.query.type': {
      handler (newVal) {
        this.tabIndex = Number(newVal)
      }
    }
  },
  mounted () {
    this.setFooterState(true)
    this.setNavState(1)
    this.setNavFontState(0)
    const tabIndex = this.$route.query.type
    if (tabIndex) {
      this.tabIndex = Number(tabIndex)
    }
    window.addEventListener('scroll', this.handleNavbarState)
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.handleNavbarState)
  },
  methods: {
    handleCheckItemLink (item) {
      this.$router.push({ path: item })
    },
    handleNavbarState () {
      const documentTop = getDocumentTop()
      if (documentTop < 10) {
        this.setNavState(1)
      } else {
        this.setNavState(0)
      }
    },
    handleCheckItem (index) {
      this.tabIndex = index
      // this.$router.push({ query: { type: index } })
    }
  }
}
</script>

<style scoped lang="scss">
@media screen and (min-width: 769px) {
  .product-container {
    min-width: 1080px;
    height: 100vh;
    position: relative;
    .content-wrapper {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: vh(100) vw(100);
      padding-left: vw(210);
      position: relative;
    }

    .left-content {
      flex: 1;
      max-width: vw(600);
      padding-right: vw(40);

      .title {
        font-size: vh(36);
        font-weight: bold;
        margin-bottom: vh(20);
        text-align: left;
        .en-title {
          font-size: vh(28);
          margin-top: vh(8);
        }
      }

      .description {
        font-size: vh(20);
        margin-bottom: vh(24);
        text-align: left;
      }

      .detail-text {
        font-size: vh(16);
        line-height: 1.8;
        margin-bottom: vh(100);
        text-align: left;
      }

      .more-info {
        font-size: vh(18);
        text-align: left;
        .link {
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    .circle1 {
      position: absolute;
      bottom: vw(40);
      left: vw(-200);
      width: vw(400);
      height: vw(400);
      border-radius: 50%;
      }
    .circle2 {
      position: absolute;
      top: vw(40);
      right: vw(-200);
      width: vw(400);
      height: vw(400);
      border-radius: 50%;
    }
    .right-content {
      flex: 1;
      display: flex;
      z-index: 1;
      justify-content: center;
      .scene-image {
        max-width: 100%;
        height: auto;
      }
    }
    .bg-gradient-to-1 {
      background: linear-gradient(137.55deg, #FFFFFF 0.56%, #FFD79B 99.44%);
      .circle1{
        background: linear-gradient(90deg, rgba(250, 255, 255, 0.16) 0%, rgba(190, 114, 0, 0.16) 100%);

      }
      .circle2{
        background: linear-gradient(90deg, rgba(59, 65, 197, 0.07) 0%, rgba(169, 129, 187, 0.07) 49%, rgba(255, 200, 169, 0.07) 100%);

      }
    }
    .bg-gradient-to-2 {
      background: linear-gradient(133.7deg, #FFEFF2 -1.84%, #DFFFDA 89.99%);
      .circle1{
        background: linear-gradient(90deg, rgba(150, 222, 218, 0.16) 0%, rgba(80, 201, 195, 0.16) 100%);
      }
      .circle2{
        background: linear-gradient(90deg, rgba(59, 65, 197, 0.07) 0%, rgba(169, 129, 187, 0.07) 49%, rgba(255, 200, 169, 0.07) 100%);
      }
    }
    .bg-gradient-to-3 {
      background: linear-gradient(131.01deg, #E4FFFE 2.96%, #D2FFFD 88.87%);
      .circle1{
        background: linear-gradient(90deg, rgba(150, 222, 218, 0.16) 0%, rgba(80, 201, 195, 0.16) 100%);
      }
      .circle2{
        background: linear-gradient(90deg, rgba(59, 65, 197, 0.07) 0%, rgba(169, 129, 187, 0.07) 49%, rgba(255, 200, 169, 0.07) 100%);
      }
    }
    .bg-gradient-to-4 {
      background: linear-gradient(126.98deg, #E3FDF5 4.1%, #FFE6FA 87.79%);
      .circle1{
        background: linear-gradient(90deg, rgba(246, 238, 249, 0.16) 0%, rgba(234, 22, 211, 0.16) 100%);
      }
      .circle2{
        background: linear-gradient(90deg, rgba(59, 65, 197, 0.07) 0%, rgba(169, 129, 187, 0.07) 49%, rgba(255, 200, 169, 0.07) 100%);
      }
    }
    .bg-gradient-to-5 {
      background: linear-gradient(124.25deg, #F8E2F0 4.89%, #FFFCDC 88.7%);
      .circle1{
        background: linear-gradient(90deg, rgba(250, 112, 154, 0.08) 0%, rgba(254, 225, 64, 0.08) 100%);
      }
      .circle2{
        background: linear-gradient(90deg, rgba(59, 65, 197, 0.07) 0%, rgba(169, 129, 187, 0.07) 49%, rgba(255, 200, 169, 0.07) 100%);
      }
    }
  }

  // 添加动画相关样式
  .animate__animated {
    animation-duration: 1s;
  }

  .animate__delay-200 {
    animation-delay: 0.2s;
  }

  .animate__delay-300 {
    animation-delay: 0.3s;
  }

  .animate__delay-400 {
    animation-delay: 0.4s;
  }

  .check-group {
    position: absolute;
    top: vh(200);
    right: vw(40);
    .check-item {
      width: vh(50);
      height: vh(50);
      border-radius: 50%;
      margin-top: vh(30);
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0;
      img{
        width: 100%;
        height: 100%;
        margin-top: 3%;
      }
      &.active {
        transform: scale(1.3);
        transition: all 0.3s ease;
        box-shadow: 0 0 vh(20) rgba(186, 181, 181, 0.3);
      }

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .product-container {
    min-height: pw(350);
    background: linear-gradient(to bottom right, #f0ffff, #e0ffff);
    // padding: pw(20) 0;
    position: relative;

    .content-wrapper {
      width: 100%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      padding: 0 pw(20);

      .left-content {
        width: 100%;
        padding-right: 0;

        .title {
          font-size: pw(24);
          font-weight: bold;
          margin-bottom: pw(15);
          text-align: center;

          .en-title {
            font-size: pw(18);
            margin-top: pw(5);
          }
        }

        .description {
          font-size: pw(16);
          margin-bottom: pw(15);
          text-align: center;
        }

        .detail-text {
          font-size: pw(14);
          line-height: 1.6;
          margin-bottom: pw(20);
          text-align: left;
        }

        .more-info {
          font-size: pw(16);
          text-align: center;
          margin-bottom: pw(20);
          a{
            color: #1890ff;
            text-decoration: underline;
          }
        }
      }

      .right-content {
        width: 100%;
        margin-top: pw(20);

        .scene-image {
          max-width: 100%;
          max-height: pw(300);
          height: auto;
        }
      }

      .circle1, .circle2 {
        display: none; // 在移动端隐藏装饰圆圈
      }
    }

    .check-group {
      position: absolute;
      bottom: pw(20);
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: pw(15);
      background: rgba(255, 255, 255, 0.8);
      padding: pw(10);
      border-radius: pw(25);
      box-shadow: 0 pw(2) pw(10) rgba(0, 0, 0, 0.1);

      .check-item {
        width: pw(40);
        height: pw(40);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        img {
          width: 100%;
          height: 100%;
        }

        &.active {
          transform: scale(1.1);
          box-shadow: 0 0 pw(10) rgba(47, 46, 46, 0.3);
        }
      }
    }

    // 针对不同背景的移动端适配
    .bg-gradient-to-r {
      background: linear-gradient(to bottom, #f0ffff, #e0ffff);
    }

    .bg-gradient-to-r-book {
      background: linear-gradient(180deg, #E3FDF5 0%, #FFE6FA 100%);
    }

    .bg-gradient-to-r-mate {
      background: linear-gradient(180deg, #F8E2F0 0%, #FFFCDC 100%);
    }
  }

  // 移动端禁用动画
  .animate__animated {
    animation: none !important;
  }
}
</style>
