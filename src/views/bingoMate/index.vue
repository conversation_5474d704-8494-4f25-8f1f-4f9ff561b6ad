<template>
  <div class="w">
    <div class="bg-color-box">
      <div class="banner-title-box">
        <div class="banner-title">
          <div class="banner-title-weight animate__animated animate__fadeIn">
            <div v-if="showTitle" class="banner-title-weight mr15 text-align">缤果数字非遗</div>
            <div class="banner-title-weight mr15">科技让文化活起来</div>
          </div>
          <div
            class="banner-title-item animate__animated animate__fadeIn animate__delay-100"
          >
            让孩子领略中华文化之美
          </div>
        </div>
        <img
          class="sky-img animate__animated animate__fadeIn animate__delay-100"
          src="../../assets/images/ai/ai-banner-1.png"
        />
      </div>

      <div
        class="banner-nav animate__animated animate__fadeIn animate__delay-200"
      >
        <div class="item-box">
          <div class="item-title">趣味化</div>
          <div class="item-desc">教学内容</div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">特色化</div>
          <div class="item-desc">非遗主题</div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">动手实践</div>
          <div class="item-desc">手工制作体验</div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">生动化</div>
          <div class="item-desc">经典名著重现</div>
        </div>
      </div>
    </div>
    <div class="computer content">
      <svg-icon
        icon-class="mate-title"
        class="title mt40 mb30  animate__animated animate__fadeIn  animate__delay-100"
      />
      <p>通过AI互动与创意元素的教学内容</p>
      <p>让学习过程更加生动有趣，激发学生的学习兴趣。</p>
      <img src="../../assets/images/mate/img1.png" alt="" class="mate-img" />

      <div class="pb100">
        <div v-animate-onscroll="'animate__animated animate__fadeInRight'" class="w1000 flex align-center">
          <img width="600" class="mr20" src="../../assets/images/mate/img2.png" />
          <div class="flex flex-col ml20">
            <div class="title-text mb20">非遗主题与经典名著融合</div>
            <p>围绕中华优秀传统文化，融入经典名著与非遗文化特色，打造独具匠心的教学体验。</p>
          </div>
        </div>
      </div>

      <div class="pb100">
        <div v-animate-onscroll="'animate__animated animate__fadeInLeft'" class="w1000 flex align-center">
          <div class="flex flex-col mr20">
            <div class="title-text mb20">在动手制作中感受传统文化</div>
            <p>通过亲手制作非遗工艺品，增强学生的动手能力和对传统文化的理解。</p>
          </div>
          <img width="600" class="ml20" src="../../assets/images/mate/img3.png" />
        </div>
      </div>

      <div class="pb100">
        <div v-animate-onscroll="'animate__animated animate__fadeInRight'" class="w1000 flex align-center justify-center">
          <img width="600" class="mr20" src="../../assets/images/mate/img4.png" />
          <div class="flex flex-col ml20">
            <div class="title-text mb20">双师AI教学模式</div>
            <p>由AI教师授课，线下教师辅助创设情境式教学。
              以创新形式演绎经典作品，让传统文化更加鲜活生动。</p>
          </div>
        </div>
      </div>
      <svg-icon
        icon-class="mate-title-02"
        class="title mt40 mb30  animate__animated animate__fadeIn  animate__delay-100"
      />
      <div class="w mb100">
        <swiper class="swiper slinter" :options="swiperOption">
          <swiper-slide v-for="(item, index) in swiperList" :key="index">
            <div class="slide-item">
              <img class="slide-img" :src="item.img" />
              <div class="slide-title">{{ item.title }}</div>
            </div>
          </swiper-slide>
        </swiper>
      </div>

    </div>
    <div class="h-5 content">
      <svg-icon
        icon-class="mate-title"
        class="title mt40 mb30  animate__animated animate__fadeIn  animate__delay-100"
      />
      <p>通过AI互动与创意元素的教学内容</p>
      <p>让学习过程更加生动有趣，激发学生的学习兴趣。</p>
      <img src="../../assets/images/mate/img1.png" alt="" class="mate-img" />

      <div class="pb50">
        <div v-animate-onscroll="'animate__animated animate__fadeInRight'" class="w1000 flex align-center">
          <img width="600" class="mr20" src="../../assets/images/mate/img2.png" />
          <div class="flex flex-col ml20">
            <div class="title-text mb20">非遗主题与经典名著融合</div>
            <p style="text-align: left;">围绕中华优秀传统文化，融入经典名著与非遗文化特色，打造独具匠心的教学体验。</p>
          </div>
        </div>
      </div>

      <div class="pb50">
        <div v-animate-onscroll="'animate__animated animate__fadeInLeft'" class="w1000 flex align-center">
          <img width="600" class="ml20" src="../../assets/images/mate/img3.png" />
          <div class="flex flex-col mr20">
            <div class="title-text mb20">在动手制作中感受传统文化</div>
            <p style="text-align: left;">通过亲手制作非遗工艺品，增强学生的动手能力和对传统文化的理解。</p>
          </div>
        </div>
      </div>

      <div class="pb50">
        <div v-animate-onscroll="'animate__animated animate__fadeInRight'" class="w1000 flex align-center justify-center">
          <img width="600" class="mr20" src="../../assets/images/mate/img4.png" />
          <div class="flex flex-col ml20">
            <div class="title-text mb20">双师AI教学模式</div>
            <p style="text-align: left;">由AI教师授课，线下教师辅助创设情境式教学。
              以创新形式演绎经典作品，让传统文化更加鲜活生动。。</p>
          </div>
        </div>
      </div>
      <svg-icon
        icon-class="mate-title-02"
        class="title1  animate__animated animate__fadeIn  animate__delay-100"
      />
      <div class="w mb20">
        <swiper class="swiper slinter" :options="swiperOptionM">
          <swiper-slide v-for="(item, index) in swiperList" :key="index">
            <div class="slide-item">
              <img class="slide-img" :src="item.img" />
              <div class="slide-title">{{ item.title }}</div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
  </div>
</template>

<script>
import { getDocumentTop } from '@/utils/index.js'
export default {
  components: {},
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  data () {
    return {
      showTitle: window.location.host !== 'bingoclass.com.cn',
      swiperList: [
        {
          img: require('@/assets/images/mate/s1.png'),
          title: '典籍里的首饰'
        },
        {
          img: require('@/assets/images/mate/s2.png'),
          title: '丹漆成器'
        },
        {
          img: require('@/assets/images/mate/s3.png'),
          title: '指尖鞠舞'
        },
        {
          img: require('@/assets/images/mate/s4.png'),
          title: '布语时光'
        },
        {
          img: require('@/assets/images/mate/s5.png'),
          title: '时光织语'
        },
        {
          img: require('@/assets/images/mate/s6.png'),
          title: '锦绣华章'
        },
        {
          img: require('@/assets/images/mate/s7.png'),
          title: '珠韵绣梦'
        },
        {
          img: require('@/assets/images/mate/s8.png'),
          title: '中药造物'
        },
        {
          img: require('@/assets/images/mate/s9.png'),
          title: '木意匠心'
        }
      ],
      swiperOption: {
        loop: true,
        freeMode: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false
        },
        speed: 6000,
        spaceBetween: 30,
        slidesPerView: 'auto'
      },
      swiperOptionM: {
        loop: true,
        freeMode: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false
        },
        speed: 6000,
        spaceBetween: 30,
        slidesPerView: 'auto'
      }
    }
  },
  mounted () {
    this.setFooterState(true)
    this.setNavState(1)
    this.setNavFontState(0)
    window.addEventListener('scroll', this.handleNavbarState)
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.handleNavbarState)
    this.setNavState(1)
  },
  methods: {
    handleNavbarState () {
      const documentTop = getDocumentTop()
      if (documentTop < 10) {
        this.setNavState(1)
      } else {
        this.setNavState(0)
      }
    },
    tagHover (key) {
      this.hoverItem = key
      this.clickItem = key
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-color-box {
  background: linear-gradient(90deg, #fcccf1 0%, #fff5ca 50%, #8fc0fd 100%);
}
@media screen and (min-width: 769px) {
    .text-align{
        text-align: left;
    }
  .h-5 {
    display: none !important;
  }

  .sky-img {
    width: 500px;
    height: 190px;
  }

  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
        width: 50%;
    }
    p{
        font-size: 26px;
        color:#333333;
        line-height: 1.5;
        text-align: center;
    }
    .mate-img{
        width: 1200px;
        margin: 0 auto;
        margin-bottom: 60px;
    }
    .w1000 {
      width: 1200px;
      justify-content: center;
      text-align: left;
      img {
        width: 500px;
        height: auto;
        object-fit: cover;
      }
      p{
        font-size: 26px;
        text-align: left;
      }
    }
    .title-text {
      font-size: 32px;
      font-weight: bold;
      background: linear-gradient(90deg, #16BFFD 0%, #CB3066 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .li-style {
      font-size: 18px;
      color: #666;
      line-height: 2;
      position: relative;
      padding-left: 20px;
      &:before {
        content: "";
        width: 8px;
        height: 8px;
        background: #8fc0fd;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .pb100 {
      padding-bottom: 100px;
    }
    .mb20 {
      margin-bottom: 20px;
    }
    .mr20 {
      margin-right: 20px;
    }
    .ml20 {
      margin-left: 20px;
    }
    .swiper {
      width: 100%;
      height: 600px;
      position: relative;
      padding: 0 20px;
      box-sizing: border-box;

      .swiper-slide {
        height: 600px;
        width: auto !important;

        .slide-item {
          width: 197px;
          height: 541px;
          position: relative;

          .slide-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .slide-title {
            position: absolute;
            bottom: -40px;
            left: 0;
            width: 100%;
            height: 30px;
            line-height: 30px;
            text-align: center;
            font-size: 16px;
            color: #333;
            background: #fff;
            border-radius: 4px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
    .text-align{
        text-align: center;
    }
  .computer {
    display: none !important;
  }

  .bg-color-box {
    height: pw(430) !important;
    .banner-title {
      padding: 0 !important;
    }
  }

  .sky-img {
    width: 96%;
    height: pw(155);
    margin-top: pw(20);
  }

  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      width: 80%;
      height: pw(40);
    }
    .title1 {
      width: 100%;
      height: pw(40);
    }
    p {
      font-size: pw(16);
      color: #333333;
      line-height: 1.8;
      text-align: center;
      padding: 0 pw(20);
      margin-bottom: pw(10);
    }

    .mate-img {
      width: 100%;
      margin: pw(30) 0;
    }

    .w1000 {
      width: 100%;
      padding: 0 pw(20);
      flex-direction: column;

      img {
        width: 100%;
        height: auto;
        margin: pw(20) 0;
        border-radius: pw(8);
      }

      .title-text {
        font-size: pw(20);
        font-weight: bold;
        background: linear-gradient(90deg, #16BFFD 0%, #CB3066 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
      }

      p {
        font-size: pw(14);
        color: #666;
        line-height: 1.8;
        text-align: center;
        padding: 0;
      }
    }

    .pb100 {
      padding-bottom: pw(40);
    }

    .mb20 {
      margin-bottom: pw(15);
    }

    .swiper {
      width: 100%;
      height: pw(400);
      position: relative;
      padding: 0 pw(20);
      box-sizing: border-box;
      margin-top: pw(30);

      .swiper-slide {
        height: pw(400);
        width: pw(146) !important;

        .slide-item {
          width: 100%;
          height: pw(400);
          position: relative;

          .slide-img {
            width: 100%;
            height: pw(350);
            object-fit: cover;
            border-radius: pw(4);
          }

          .slide-title {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: pw(40);
            line-height: pw(40);
            text-align: center;
            font-size: pw(14);
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
