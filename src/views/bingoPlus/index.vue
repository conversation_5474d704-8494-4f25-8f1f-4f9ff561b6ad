<template>
  <div class="w">
    <div class="bg-color-box">
      <div class="banner-title-box">
        <div class="banner-title">
          <div class="animate__animated animate__fadeIn">
            <div v-if="showTitle" class="banner-title-weight mr15 text-align">缤果融合出版</div>
            <div class="banner-title-weight mr15">科技让知识触达更高效</div>
          </div>
          <!-- <div class="banner-title-weight animate__animated animate__fadeIn">科技让教育更有趣</div> -->
          <div class="banner-title-item animate__animated animate__fadeIn animate__delay-100">让每个孩子的个性得到完美绽放</div>
        </div>
        <svg-icon icon-class="sz-1" class="sky-img animate__animated animate__fadeIn animate__delay-100" />
      </div>

      <div class="banner-nav animate__animated animate__fadeIn animate__delay-200">
        <div class="item-box">
          <div class="item-title">
            优质输出
          </div>
          <div class="item-desc">
            数字化高质量供给
          </div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">
            创新呈现
          </div>
          <div class="item-desc">
            技术重塑传统出版物
          </div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">
            双效俱佳
          </div>
          <div class="item-desc">
            纸数一体融合出版
          </div>
        </div>
        <div class="item-line"></div>
        <div class="item-box">
          <div class="item-title">
            情景课堂
          </div>
          <div class="item-desc">
            故事化闯关式学习
          </div>
        </div>
      </div>
    </div>

    <div class="computer content">
      <div class="pt50 pb50 flex justify-center">
        <div class="w1000 tc">
          <svg-icon icon-class="sz-text-1" class="w600 h90" />
        </div>
      </div>

      <div class="pb100 flex justify-center">
        <div v-animate-onscroll="'animate__animated animate__fadeInLeft'" class="w1000 flex align-center">
          <img class="mr20" width="378" src="../../assets/images/sz/1.png" />
          <!-- <div class="sz-tag1">出版物</div> -->
          <div class="flex flex-col mr20">
            <svg-icon icon-class="ai-t-1" class="w130 h60 mb20" />
            <ul>
              <li class="li-style f18 fb">依托<span class="color1 f27">出版社</span>的纸质出版物。</li>
              <li class="li-style f18 fb">保障<span class="color1 f27">数字化课程</span>内容科学权威。</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="ai-bg-box flex justify-center align-center">
        <div class="flex justify-center">
          <div class="w1000 flex justify-between">
            <div class="flex flex-col tl">
              <svg-icon icon-class="ai-text-02" class="w270 h120" />
              <div class="color-white f20 mt20">
                AI赋能教学全环节
              </div>
            </div>
            <div class="w660">
              <div class="flex align-center justify-between mb30">
                <div v-animate-onscroll="'animate__animated animate__fadeIn'" class="flex flex-col align-center">
                  <img width="320" src="../../assets/images/ai/2.png" />
                  <div class="w320 flex align-center pl10">
                    <svg-icon icon-class="ai-num-1" class="w40 h60 mr20" />
                    <span class="color-white f16">智能排课、选课、备课</span>
                  </div>
                </div>
                <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-300'" class="flex flex-col align-center">
                  <img width="320" src="../../assets/images/ai/3.png" />
                  <div class="w320 flex align-center pl10">
                    <svg-icon icon-class="ai-num-2" class="w40 h60 mr20" />
                    <span class="color-white f16">AI教师多感官情景授课</span>
                  </div>
                </div>
              </div>
              <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-500'" class="flex align-center justify-between">
                <div class="flex flex-col align-center">
                  <img width="320" src="../../assets/images/ai/4.png" />
                  <div class="w320 flex align-center pl10">
                    <svg-icon icon-class="ai-num-3" class="w40 h60 mr20" />
                    <span class="color-white f16">多元化考评练习激励机制</span>
                  </div>
                </div>
                <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-700'" class="flex flex-col align-center">
                  <img width="320" src="../../assets/images/ai/5.png" />
                  <div class="w320 flex align-center pl10">
                    <svg-icon icon-class="ai-num-4" class="w40 h60 mr20" />
                    <span class="color-white f16">学情报告数据多维统计分析</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pt100 pb100 flex justify-center animate__animated animate__fadeInLeft">
        <div class="w1000 tl">
          <svg-icon icon-class="ai-text-01" class="w510 h90" />
        </div>
      </div>

      <div class="flex justify-center mb60">
        <div class="w1000 flex justify-between">
          <div class="flex flex-col w450">
            <div v-animate-onscroll="'animate__animated animate__fadeInUp'" class="f35 fb color333 w tl mb30">
              双翼驱动，构建高质量课堂
            </div>
            <div v-animate-onscroll="'animate__animated animate__fadeInUp animate__delay-300'" class="color-text f20 w tl lh30 mb60">
              线上素质领域专家、专业教师沉浸式主导授课。<br />
              线下带班教师补位助教，轻松组织管理班级。
            </div>
            <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-600'" class="f35 fb color333 w tl mb10">
              低门槛落地，轻课减负减压
            </div>
            <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-700'" class="color-text f20 w tl lh30">
              减轻教师备课负担，<br />
              降低学校开课门槛。
            </div>
          </div>

          <div class="w450 flex flex-col">
            <img v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-500'" class="w450 mb80" src="../../assets/images/ai/1.png" />
          </div>
        </div>
      </div>
      <div class="pb100 flex justify-center">
        <div v-animate-onscroll="'animate__animated animate__fadeInRight'" class="w1000 flex align-center">
          <img width="355" class="mr20" src="../../assets/images/sz/3.png" />
          <div class="flex flex-col ml20">
            <svg-icon icon-class="ai-t-3" class="w300 h60 mb20" />
            <!-- <div class="w230 sz-tag3 mb20">数字化教学内容</div> -->
            <ul>
              <li class="li-style f18 fb"><span class="color3 f27">动画</span>贯穿演绎、故事化情景呈现，亲近学生。</li>
              <li class="li-style f18 fb">采用电影级制作，规模化供给高品质输出。</li>
              <li class="li-style f18 fb">构建知识体系，丰富教学内涵。</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="pt50 pb100 flex justify-center">
        <div class="w1000 flex flex-col align-center bg-1">
          <div class="w flex justify-around align-center">
            <div class="w450 flex flex-col align-center align-self-end">
              <div class="w mb20 flex justify-around">
                <img width="140" src="../../assets/images/sz/4.png" />
                <div class="ml20 flex flex-col align-start justify-around">
                  <div class="f12 color000">《中华优秀传统文化》读本</div>
                  <div class="f24 fb color000">黑与白的和谐之道</div>
                  <img width="250" src="../../assets/images/sz/5.png" />
                </div>
              </div>
              <!-- <div class="w">纸质出版物</div> -->
            </div>
            <!-- <svg-icon icon-class="sz-arrow" class="w80 h54" /> -->
            <img id="gif" class="h50" src="../../assets/images/arrow.gif" />
            <div class="flex flex-col align-center align-self-star">
              <div class="w mb20 flex justify-between">
                <div class="w310 h200">
                  <video-js ref="video" :options="videoOptions" />
                </div>
              </div>
              <!-- <div class="w">AI数字化内容</div> -->
            </div>
          </div>
          <div class="w flex justify-around align-center mt10">
            <div class="w500">纸质出版物</div>
            <div class="w100"></div>
            <div class="w350">AI数字化内容</div>
          </div>
        </div>
      </div>

      <div class="sz-bg-box flex justify-center align-center">
        <div class="flex justify-center">
          <div class="w1000 flex flex-col justify-between">
            <div class="flex flex-col tl">
              <svg-icon icon-class="sz-text-2" class="w270 h120" />
            </div>

            <div class="w flex justify-between mb40">
              <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-100'" class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-1" class="w100 h100 mb80" />
                <div class="tl">
                  <div class="t-title">增加学生学习兴趣</div>
                  <div class="t-desc">AI教师赋能学校教师授课、游戏化教学、沉浸式体验，增强学习兴趣。</div>
                </div>
              </div>
              <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-300'" class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-2" class="w100 h100 mb80" />
                <div class="tl">
                  <div class="t-title">降低教师教学难度</div>
                  <div class="t-desc">一键备课、一键上课，大幅减少教师教学工作量，最大限度为老师减负。</div>
                </div>
              </div>
            </div>

            <div class="w flex justify-between">
              <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-500'" class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-3" class="w100 h100 mb80" />
                <div class="tl">
                  <div class="t-title">降低学校开课难度</div>
                  <div class="t-desc">赋能学校开设高质量传统文化等素质教育课程，补充学校思政教育。</div>
                </div>
              </div>
              <div v-animate-onscroll="'animate__animated animate__fadeIn animate__delay-700'" class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-4" class="w100 h100 mb80" />
                <div class="tl">
                  <div class="t-title">快速在课堂上普及</div>
                  <div class="t-desc">纸数融合，快速高效开课。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="h-5 content">
      <div class="pt30 pb30 flex justify-center">
        <div class="w tc">
          <svg-icon icon-class="sz-text-1" class="w320 h46" />
        </div>
      </div>

      <div class="mb30 flex justify-center">
        <div class="w flex flex-col justify-center align-center">
          <!-- <div class="sz-tag1">出版物</div> -->
          <svg-icon icon-class="ai-t-1" class="w80 h36 mb20" />
          <ul>
            <li class="li-style f18 fb">依托<span class="color1 f23">出版社</span>的纸质出版物。</li>
            <li class="li-style f18 fb">保障<span class="color1 f23">数字化课程</span>内容科学权威。</li>
          </ul>
          <img class="w380 h220" src="../../assets/images/sz/1.png" />
        </div>
      </div>
      <div class="ai-bg-box flex justify-center align-center mb20">
        <div class="w pl10 pr10 box-border">
          <svg-icon icon-class="ai-m-text-02" class="w290 h46" />
          <div class="color-white f22 mt10 mb30">
            AI赋能教学全环节
          </div>
          <el-row :gutter="10" class="mb20">
            <el-col :span="12">
              <div class="w flex flex-col align-center">
                <img class="w" src="../../assets/images/ai/2.png" />
                <div class="w flex align-center pl5">
                  <svg-icon icon-class="ai-num-1" class="w20 h36 mr5" />
                  <span class="color-white f12">智能排课、选课、备课</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="w flex flex-col align-center">
                <img class="w" src="../../assets/images/ai/3.png" />
                <div class="w flex align-center pl5">
                  <svg-icon icon-class="ai-num-2" class="w20 h36 mr5" />
                  <span class="color-white f12">AI教师多感官情景授课</span>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="10" class="mb20">
            <el-col :span="12">
              <div class="w flex flex-col align-center">
                <img class="w" src="../../assets/images/ai/4.png" />
                <div class="w flex align-center pl5">
                  <svg-icon icon-class="ai-num-3" class="w20 h36 mr5" />
                  <span class="color-white f12">多元化考评练习激励机制</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="w flex flex-col align-center">
                <img class="w" src="../../assets/images/ai/5.png" />
                <div class="w flex align-center pl5">
                  <svg-icon icon-class="ai-num-4" class="w20 h36 mr5" />
                  <span class="color-white f12">学情报告数据多维统计分析</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="w flex justify-center mb20">
        <div class="w flex flex-col">
          <div class="flex flex-col w mb30">
            <div class="f20 fb color333 w tc mb20">
              双翼驱动，构建高质量课堂
            </div>
            <div class="color-text f14 w tc lh20">
              线上素质领域专家、专业教师沉浸式主导授课。<br />
              线下带班教师补位助教，轻松组织管理班级。
            </div>
          </div>

          <div class="w flex flex-col">
            <img class="w pl10 pr10 box-border mb20" src="../../assets/images/ai/1.png" />
            <div class="f20 fb color333 w tc mb20">
              低门槛落地，轻课减负减压
            </div>
            <div class="color-text f14 w tc lh20">
              减轻教师备课负担，<br />
              降低学校开课门槛。
            </div>
          </div>
        </div>
      </div>

      <div class="mb30 flex justify-center">
        <div class="w flex flex-col justify-center align-center">
          <div class="flex flex-col align-center">
            <svg-icon icon-class="ai-t-3" class="w180 h36 mb20" />
            <ul>
              <li class="li-style f16 fb"><span class="color3 f23">动画</span>贯穿演绎、故事化情景呈现，亲近学生。</li>
              <li class="li-style f16 fb">采用电影级制作，规模化供给高品质输出。</li>
              <li class="li-style f16 fb">构建知识体系，丰富教学内涵。</li>
            </ul>
          </div>
          <img class="w350 h220 mt10 b-r-17" src="../../assets/images/sz/3.png" />
        </div>
      </div>

      <div class="pt50 flex justify-center pr10 pl10 box-border">
        <div class="w flex flex-col align-center bg-1">
          <div class="w flex flex-col align-center">
            <div class="w flex flex-col align-center align-self-end">
              <div class="w mb20 flex justify-around">
                <img class="w140 h200" src="../../assets/images/sz/4.png" />
                <div class="ml20 flex flex-col align-start justify-around">
                  <div class="f12 color000">《中华优秀传统文化》读本</div>
                  <div class="f24 fb color000">黑与白的和谐之道</div>
                  <img class="w200 h120" src="../../assets/images/sz/5.png" />
                </div>
              </div>
              <div class="w color333 f15 fb">纸质出版物</div>
            </div>
            <svg-icon icon-class="ai-m-arrow-down" class="w80 h54 mt20 mb20" />
            <div class="w flex flex-col align-center align-self-star">
              <div class="w mb20 flex justify-between">
                <div class="w h200">
                  <video-js ref="video" :options="videoOptions" />
                </div>
              </div>
              <!-- <div class="w">AI数字化内容</div> -->
            </div>
            <div class="w color333 f15 fb mb20">AI数字化内容</div>
          </div>
        </div>
      </div>

      <div class="sz-bg-box flex justify-center align-center">
        <div class="flex justify-center">
          <div class="w flex flex-col justify-between">
            <div class="flex flex-col tl mb10">
              <svg-icon icon-class="sz-text-2" class="w110 h40" />
            </div>

            <div class="w flex flex-col">
              <div class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-1" class="w70 h70 mb40" />
                <div class="tl">
                  <div class="t-title">增加学生学习兴趣</div>
                  <div class="t-desc">AI教师赋能学校教师授课、游戏化教学、沉浸式体验，增强学习兴趣。</div>
                </div>
              </div>
              <div class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-2" class="w70 h70 mb40" />
                <div class="tl">
                  <div class="t-title">降低教师教学难度</div>
                  <div class="t-desc">一键备课、一键上课，大幅减少教师教学工作量，最大限度为老师减负。</div>
                </div>
              </div>
            </div>

            <div class="w flex flex-col">
              <div class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-3" class="w70 h70 mb40" />
                <div class="tl">
                  <div class="t-title">降低学校开课难度</div>
                  <div class="t-desc">赋能学校开设高质量传统文化等素质教育课程，补充学校思政教育。</div>
                </div>
              </div>
              <div class="tag-box flex flex-col justify-between align-start">
                <svg-icon icon-class="sz-icon-4" class="w70 h70 mb40" />
                <div class="tl">
                  <div class="t-title">快速在课堂上普及</div>
                  <div class="t-desc">纸数融合，快速高效开课。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div></template>

<script>
import { getDocumentTop } from '@/utils/index.js'
import videoJs from '@/components/videoJs.vue'
import V1 from '../../assets/video/v-1.mp4'
import coverImg from '../../assets/images/sz/6.png'
export default {
  components: {
    videoJs
  },
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  data () {
    return {
      V1,
      showTitle: window.location.host !== 'bingoclass.com.cn',
      coverImg,
      time: null,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false,
        loop: false,
        poster: coverImg,
        sources: [{
          src: V1,
          type: 'video/mp4'
        }]
      }
    }
  },
  mounted () {
    this.setFooterState(true)
    this.setNavState(1)
    this.setNavFontState(0)
    window.addEventListener('scroll', this.handleNavbarState)
    this.gifMove()
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.handleNavbarState)
    this.setNavState(1)
    if (this.time) {
      clearInterval(this.time)
    }
  },
  methods: {
    handleNavbarState () {
      const documentTop = getDocumentTop()
      if (documentTop < 10) {
        this.setNavState(1)
      } else {
        this.setNavState(0)
      }
    },
    gifMove () {
      this.time = setInterval(() => {
        document.getElementById('gif').src = document.getElementById('gif').src
      }, 2000)
    }
  }
}

</script>

  <style lang="scss" scoped>
  .bg-color-box {
    background: linear-gradient(90deg, #529DFF 0%, #30FAD2 100%);
  }
  @media screen and (min-width: 769px) {
    .text-align{
        text-align: left;
    }
    .h-5 {
      display: none !important;
    }

    .sky-img {
      width: 480px;
      height: 300px;
    }

    .content {
      width: 100%;
      .title{
        font-size: 30px;
        text-align: left;
        margin-bottom: pw(5);
        font-weight: bold;
      }
      .ai-bg-box {
      width: 100%;
      height: 800px;
      background: url('../../assets/images/ai/ai-bg.png') no-repeat;
      background-size: cover;
    }
      .desc{
        font-size: 20px;
        color: #828282;
        text-align: left;
        margin-bottom: pw(5);
      }
      .color333 {
        color: #333333;
      }

      .color000 {
        color: #000000;
      }

      .color-text {
        color: #828282;
      }
      .color-white {
        color: #fff;
      }

      .color-d9 {
        color: #BB6BD9;
      }
      .f35 {
        font-size: 35px;
      }

      .mb250 {
        margin-bottom: 250px;
      }

      .mb200 {
        margin-bottom: 200px;
      }

      .sz-tag1, .sz-tag2, .sz-tag3 {
        padding: 5px;
        border-radius: 5px;
        font-size: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sz-tag1 {
        color: #56CCF2;
        border: 2px solid #56CCF2;
      }

      .sz-tag2 {
        color: #EB5757;
        border: 2px solid #EB5757;
      }

      .sz-tag3 {
        color: #27AE60;
        border: 2px solid #27AE60;
      }

      .color1 {
        color: #2D9CDB;
      }
      .color2 {
        color: #EB5757;
      }
      .color3 {
        color: #27AE60;
      }

      .li-style {
        list-style-type: disc;
        margin: 10px 0;
        text-align: left;
      }

      .bg-1 {
        background: url('../../assets/images/sz/bg-1.png') no-repeat;
        background-size: contain;
        width: 1000px;
        height: 480px;
        box-sizing: border-box;
        padding: 120px 20px 20px 10px;
      }

      .sz-bg-box {
        width: 100%;
        height: 1200px;
        background: url('../../assets/images/sz/bg-2.jpg') no-repeat;
        background-size: cover;
      }

      .tag-box {
        width: 49%;
        height: 367.5px;
        background: rgba(0, 0, 0, 0.82);
        border-radius: 22.5px;
        box-sizing: border-box;
        padding: 50px 15px;

        .t-title {
          font-weight: 500;
          font-size: 36px;
          color: #fff;
          margin-bottom: 10px;
        }
        .t-desc {
          font-weight: 500;
          font-size: 14px;
          color: #BDBDBD;
        }
      }
    }
  }
  @media screen and (max-width: 768px) {
    .text-align{
        text-align: center;
    }
    .computer {
      display: none !important;
    }
    .bg-color-box {
      .banner-title {
        padding-top: 0 !important;
      }
      .banner-nav {
        .item-box {
          width: pw(150) !important;
        }
      }
    }

    .h220 {
      height: pw(230);
    }

    .sky-img {
      width: pw(280);
      height: pw(180);
    }

    .content {
      width: 100%;
      .title{
        font-size: 20px;
        color: #333333;
        text-align: center;
        margin-bottom: pw(10);
        font-weight: bold;
        margin-top: pw(10);
      }
      .desc{
        font-size: 14px;
        color: #828282;
        text-align: center;
        margin-bottom: pw(10);
      }

      .color000 {
        color: #000000;
      }

      .color-text {
        color: #828282;
      }
      .color-white {
        color: #fff;
      }

      .color-d9 {
        color: #BB6BD9;
      }
      .ai-bg-box {
      width: 100%;
      height: pw(600);
      background: url('../../assets/images/ai/ai-bg.png') no-repeat;
      background-size: cover;
    }

    .ai-tag1, .ai-tag2, .ai-tag3, .ai-tag4, .ai-tag5 {
      width: pw(85);
      height: pw(85);
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: pw(30);
      font-size: pw(24);
      font-weight: 600;
    }
      .sz-tag1, .sz-tag2, .sz-tag3 {
        padding: 5px;
        border-radius: 5px;
        font-size: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sz-tag1 {
        color: #56CCF2;
        border: 2px solid #56CCF2;
      }

      .sz-tag2 {
        color: #EB5757;
        border: 2px solid #EB5757;
      }

      .sz-tag3 {
        color: #27AE60;
        border: 2px solid #27AE60;
      }

      .color1 {
        color: #56CCF2;
      }
      .color2 {
        color: #EB5757;
      }
      .color3 {
        color: #27AE60;
      }

      .li-style {
        list-style-type: disc;
        margin: 10px 0;
        text-align: left;
      }

      .b-r-17 {
        border-radius: pw(17);
      }

      .bg-1 {
        background: linear-gradient(180deg, #D9F6FF 20.96%, rgba(255, 255, 255, 0) 100%);
        width: 100%;
        // height: pw(633);
        box-sizing: border-box;
        padding: pw(30);
        padding-bottom: pw(10);
        border-radius: pw(17);
      }

      .sz-bg-box {
        width: 100%;
        // height: pw(1200);
        padding: pw(20) pw(10);
        box-sizing: border-box;
        background: url('../../assets/images/sz/bg-2.jpg') no-repeat;
        background-size: cover;
      }

      .tag-box {
        width: 100%;
        // height: pw(360);
        background: rgba(0, 0, 0, 0.82);
        border-radius: pw(15);
        box-sizing: border-box;
        padding: pw(30) pw(15);
        margin-bottom: pw(20);

        .t-title {
          font-weight: 500;
          font-size: pw(20);
          color: #fff;
          margin-bottom: pw(20);
        }
        .t-desc {
          font-weight: 500;
          font-size: pw(14);
          color: #BDBDBD;
        }
      }
    }
  }

  </style>
