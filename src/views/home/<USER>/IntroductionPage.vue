<!-- 公司介绍 -->
<template>
  <div class="introduction">
    <svg-icon v-if="showAnimate" icon-class="logo-white" class="icon animate__animated animate__fadeInDown" />
    <div v-if="showAnimate" class="intro animate__animated animate__delay-100 animate__fadeInRight">
      <p>
        缤果科技是一家深耕融合出版领域的创新企业，致力于推动出版行业的智能化、个性化与普惠化。公司秉持“用科技打造融合出版新模式”的使命，以技术创新为引擎，构建“内容—资源—场景—数据”的全链路闭环，打造可适配多元出版场景的智能创作与分发体系，推动优质内容的高效生产与广泛传播，助力出版资源的精准供给与普惠共享。
      </p>
    </div>
    <div class="footer">
      <span class="mr24">{{`ICP备案号：${icp}`}}</span>
      <span class="mr24"><EMAIL></span>
      <span class="mr24">400-168-0260</span>
      <span>北京英华高科科技有限公司 版权所有</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showAnimate: {
      type: Boolean,
      require: true
    }
  },
  data () {
    return {
      icp: '京ICP备18001616号-8'
    }
  },
  mounted () {
    if (window.location.host.indexOf('bingomate.cn') > -1) {
      this.icp = '京ICP备18001616号-9'
    } else {
      this.icp = '京ICP备18001616号-8'
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {
.introduction {
  min-width: 1080px;
  height: 100%;
  background: url('~assets/images/home/<USER>') center center no-repeat;
  background-size: cover;
  position: relative;
  overflow: hidden;
  display: flex;
  padding: vh(280) 0 0 vw(195);
  // overflow: scroll;

  .icon {
    width: vh(242);
    height: vh(126);
    object-fit: contain;
    margin-right: vw(130);
  }

  .intro {
    font-family: 'PingFang SC';
    font-weight: 400;
    width: vw(615);
    color: rgba(255, 255, 255, 0.9);
    text-align: start;
    line-height: vh(30);
    font-size: vh(18);
    min-width: 475px;
  }
  .footer {
        height: 60px;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background: rgba(251, 251, 255, 0.04);

        span {
            line-height: 60px;
            font-size: 14px;
            color: white;
        }
    }
}
}

@media screen and (max-width: 768px) {
.animate__animated {
  animation: none;
}

.introduction {
  width: 100%;
  background: url('~assets/images/home/<USER>') center center no-repeat;
  background-size: cover;
  position: relative;
  display: flex;
  padding: pw(50) pw(25) pw(25);

  .icon {
    display: none;
  }

  .intro {
    width: pw(380);
    font-family: 'PingFang SC';
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    text-align: start;
    line-height: pw(20);
    font-size: pw(14);
  }
  .footer {
    display: none;
  }
}
}
</style>
