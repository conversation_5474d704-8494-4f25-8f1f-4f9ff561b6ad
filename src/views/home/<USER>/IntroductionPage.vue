<!-- 公司介绍 -->
<template>
  <div class="introduction">
    <svg-icon v-if="showAnimate" icon-class="logo-white" class="icon animate__animated animate__fadeInDown" />
    <div v-if="showAnimate" class="intro animate__animated animate__delay-100 animate__fadeInRight">
      <p>
        缤果科技是一家以人工智能技术为核心驱动力的创新型企业，专注于AI大模型、多模态交互、内容智能生成与具身智能体等前沿技术的研发与产业化应用。公司构建了以“内容—模型—交互—场景”为核心的技术架构，打造覆盖“直播互动-交互数字人-AI智能体-具身智能“全链条的教育垂域智能产品体系，为行业提供可规模化、可落地的智能解决方案和产品。
      </p>
      <p>
        依托自主研发的智能交互技术和产品，缤果科技已在融合出版、数字教材、文化传承、智慧教学、家庭学习、社区服务等多个场景实现深度融合与大规模落地应用。公司持续推动AI与行业实践融合，构建开放、可持续、具竞争力的智能生态，赋能行业实现高质量转型与普惠化发展。
      </p>
    </div>
    <div class="footer">
      <span class="mr24">{{`ICP备案号：${icp}`}}</span>
      <span class="mr24"><EMAIL></span>
      <span class="mr24">400-168-0260</span>
      <span>北京英华高科科技有限公司 版权所有</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showAnimate: {
      type: Boolean,
      require: true
    }
  },
  data () {
    return {
      icp: '京ICP备18001616号-8'
    }
  },
  mounted () {
    if (window.location.host.indexOf('bingomate.cn') > -1) {
      this.icp = '京ICP备18001616号-9'
    } else {
      this.icp = '京ICP备18001616号-8'
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {
.introduction {
  min-width: 1080px;
  height: 100%;
  background: url('~assets/images/home/<USER>') center center no-repeat;
  background-size: cover;
  position: relative;
  overflow: hidden;
  display: flex;
  padding: vh(280) 0 0 vw(195);
  // overflow: scroll;

  .icon {
    width: vh(242);
    height: vh(126);
    object-fit: contain;
    margin-right: vw(130);
  }

  .intro {
    font-family: 'PingFang SC';
    font-weight: 400;
    width: vw(615);
    color: rgba(255, 255, 255, 0.9);
    text-align: start;
    line-height: vh(30);
    font-size: vh(18);
    min-width: 475px;
  }
  .footer {
        height: 60px;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background: rgba(251, 251, 255, 0.04);

        span {
            line-height: 60px;
            font-size: 14px;
            color: white;
        }
    }
}
}

@media screen and (max-width: 768px) {
.animate__animated {
  animation: none;
}

.introduction {
  width: 100%;
  background: url('~assets/images/home/<USER>') center center no-repeat;
  background-size: cover;
  position: relative;
  display: flex;
  padding: pw(50) pw(25) pw(25);

  .icon {
    display: none;
  }

  .intro {
    width: pw(380);
    font-family: 'PingFang SC';
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    text-align: start;
    line-height: pw(20);
    font-size: pw(14);
  }
  .footer {
    display: none;
  }
}
}
</style>
