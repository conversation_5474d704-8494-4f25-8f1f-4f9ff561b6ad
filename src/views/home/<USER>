<template>
  <div class="home">
    <div v-if="!isMobile" class="computer">
      <full-page id="fullpage" ref="fullpage" :options="fullPageOpts">
        <div class="section">
          <first-page :show-animate="showFirstAnimate" />
        </div>
        <div class="section">
          <secend-page :show-animate="showSecendAnimate" />
        </div>
        <div class="section">
          <third-page :show-animate="showThirdAnimate" />
        </div>
        <div class="section">
          <introduction-page :show-animate="showIntroAnimate" />
        </div>
      </full-page>
      <div class="navigation flex flex-col">
        <div v-for="item in 4" :key="item" class="nav" :class="{'full-opacity': item === currentPage + 1}" @click="moveTo(item)"></div>
      </div>
      <div v-if="currentPage !== 3" class="next-btn flex justify-center align-center" @click="moveNextPage">
        <svg-icon icon-class="arrow-down" class="arrow-down" />
      </div>
    </div>

    <div v-if="isMobile" class="h5">
      <first-page :show-animate="true" />
      <secend-page :show-animate="true" />
      <third-page :show-animate="true" />
      <introduction-page :show-animate="true" />
    </div>
  </div>
</template>

<script>
import FirstPage from './components/FirstPage.vue'
import IntroductionPage from './components/IntroductionPage.vue'
import SecendPage from './components/SecendPage.vue'
import ThirdPage from './components/ThirdPage.vue'
export default {
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  components: { FirstPage, IntroductionPage, SecendPage, ThirdPage },
  data () {
    return {
      fullPageOpts: {
        licenseKey: 'YOUR_KEY_HERE',
        afterLoad: this.afterLoad,
        scrollOverflow: true,
        scrollBar: false,
        menu: '#menu',
        navigation: true,
        scrollingSpeed: 400,
        sectionsColor: [
          '#ffffff',
          '#ffffff',
          '#ffffff',
          '#ffffff',
          '#ffffff',
          '#ffffff',
          '#ffffff'
        ],
        lockAnchors: true
      },
      currentPage: 0,
      showFirstAnimate: true,
      showIntroAnimate: false,
      showSkyClassAnimate: false,
      showAIClassAnimate: false,
      DigitClassAnimate: false,
      showQingGuoClassAnimate: false,
      showResearchAnimate: false,
      showTechnicialAnimate: false,
      showSecendAnimate: false,
      showThirdAnimate: false,
      isMobile: false
    }
  },
  mounted () {
    this.setFooterState(false)
    this.setNavState(1)
    this.setNavFontState(1)
    this.checkIsMobile()
  },
  methods: {
    afterLoad: function (anchors, item) {
      this.currentPage = item.index
      this.showFirstAnimate = this.currentPage === 0
      // this.showSkyClassAnimate = this.currentPage === 2
      // this.showAIClassAnimate = this.currentPage === 3
      // this.DigitClassAnimate = this.currentPage === 4
      // this.showQingGuoClassAnimate = this.currentPage === 5
      this.showResearchAnimate = this.currentPage === 4
      // this.showTechnicialAnimate = this.currentPage === 7
      this.showSecendAnimate = this.currentPage === 1
      this.showThirdAnimate = this.currentPage === 2
      this.showIntroAnimate = this.currentPage === 3
    },
    checkIsMobile () {
      if (document.body.clientWidth < 769) {
        this.isMobile = true
        this.setFooterState(true)
      } else {
        this.isMobile = false
        this.setFooterState(false)
      }
      window.onresize = () => {
        return (() => {
          if (document.body.clientWidth < 769) {
            this.isMobile = true
            this.setFooterState(true)
          } else {
            this.isMobile = false
            this.setFooterState(false)
          }
        })()
      }
    },
    moveNextPage () {
      if (this.currentPage === 6) {
        this.moveTo(1)
      } else {
        this.$refs.fullpage.api.moveSectionDown()
      }
    },
    moveTo (index) {
      this.$refs.fullpage.api.moveTo(index)
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {

.h5 {
  display: none;
}

.computer {
  width: 100%;
  height: 100%;
}

.home {
  height: 100%;
  width: 100%;
}

.section {
  width: 100%;
  height: 100%;
}

.navigation {
  position: absolute;
  right: vw(56);
  transform: translate(0, -50%);
  gap: vw(14);
  top: 50%;
}

.nav {
  width: vw(7);
  height: vw(7);
  border-radius: 50%;
  background: #FFFFFF;
  opacity: 0.3;
  cursor: pointer;
}

.full-opacity {
  opacity: 1;
}

.next-btn {
  position: absolute;
  width: vh(40);
  height: vh(40);
  border: 0.599688px solid rgba($color: #FFFFFF, $alpha: 0.2);
  border-radius: 12px;
  bottom: vh(20);
  transform: translate(-50%, 0);
  left: 50%;
  cursor: pointer;

  .arrow-down {
    width: vh(18);
    height: vh(10);
    animation: bounce 1.5s infinite 0s both;
    transform-origin: center bottom;
  }

  @keyframes bounce {
    0%,
    7%,
    25%,
    36%,
    45%,
    100% {
      animation-timing-function: ease-out;
      transform: translate3d(0, 0, 0);
    }
    15%,
    16% {
      animation-timing-function: ease-in;
      transform: translate3d(0, 3px, 0);
    }
  }
}
}

@media screen and (max-width: 769px) {
.home {
  height: 100%;
  width: 100%;
}
.h5 {
  width: 100%;
  height: 100%;
  overflow: scroll;
  // @include noScrollBar;
}

.computer {
  display: none;
}
}
</style>
