<template>
  <div class="first-page">
    <div class="circle1"></div>
    <!-- <img class="bg1" :src="FirstDeco" alt="" /> -->
    <!-- <svg-icon icon-class="bingo" class="bg2" /> -->
    <div v-if="showAnimate" class="title animate__animated animate__fadeInDown">使命：用科技打造融合出版新模式</div>
    <div class="circle2"></div>
  </div>
</template>

<script>
export default {
  props: {
    showAnimate: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    return {
    }
  }
}
</script>

  <style lang="scss" scoped>
  @media screen and (min-width: 769px) {
  .first-page {
      min-width: 1080px;
      height: 100%;
      // background: linear-gradient(90deg, #330867 0%, #30CFD0 100%);
      background: url('../../../assets/images/home/<USER>') no-repeat center/cover;
      // background-size: 100% 100%;
      position: relative;
      overflow: hidden;
  }

  .bg1 {
      position: absolute;
      width: vh(494);
      height: vh(494);
      left: vw(50);
      top: vh(115);
      object-fit: contain;
  }

  .text-bg {
    position: absolute;
    width: vh(547);
    height: vh(178);
    left: vw(175);
    top: vh(225);
    object-fit: contain;
    background: #fff;
    opacity: 0.1;
    border-radius: 0 0 vh(200) 0;
  }
  .bg2 {
      position: absolute;
      width: vh(550);
      height: vh(274);
      left: vw(825);
      top: vh(485);
      object-fit: contain;
      display: none;
  }

  .title {
      position: absolute;
      top: vh(344);
      left: vw(245);
      font-family: 'PingFang SC';
      font-weight: 600;
      color: #FFFFFF;
      font-size: vh(40);
  }

  .subtitle {
      position: absolute;
      top: vh(345);
      left: vw(245);
      font-family: 'PingFang SC';
      color: #FFFFFF;
      font-size: vh(26);
  }

  .circle1 {
    position: absolute;
    width: vh(464);
    height: vh(464);
    left: vw(-234);
    top: vh(432);
    opacity: 0.1;
    border: 0.74961px solid #C7D9FF;
    border-radius: 50%;
  }

  .circle2 {
      position: absolute;
      width: vh(810);
      height: vh(810);
      right: vw(-256);
      bottom: vh(-131);
      opacity: 0.2;
      border: 0.74961px solid #FFEACB;
      border-radius: 50%;
      display: none;
  }

  .bear {
      position: absolute;
      left: vw(950);
      top: vh(331);
      width: vh(340);
      height: vh(326);
      object-fit: contain;
      display: none;
  }
  }

  @media screen and (max-width: 768px) {
  .animate__animated {
    animation: none;
  }

  .first-page {
      width: 100%;
      height: pw(350);
      background: url('../../../assets/images/home/<USER>') no-repeat center/cover;
      position: relative;
      overflow: hidden;
  }

  .circle1 {
    display: none;
  }

  .bg1 {
    width: pw(177);
    height: pw(177);
    object-fit: contain;
    position: absolute;
    top: pw(18);
    left: pw(15);
  }

  .bg2 {
    width: pw(256);
    height: pw(127);
    object-fit: contain;
    position: absolute;
    transform: translate(-50%, 0);
    left: 50%;
    bottom: pw(5);
  }

  .title {
    width: 100%;
    position: absolute;
    transform: translate(-50%, 0);
    top: pw(40);
    left: 50%;
    font-family: 'PingFang SC';
    font-weight: 600;
    color: #FFFFFF;
    font-size: pw(30);
    line-height: pw(45);
    text-align: left;
    margin-left: pw(20);
  }

  .subtitle {
    width: 100%;
    position: absolute;
    transform: translate(-50%, 0);
    top: pw(98);
    left: 50%;
    font-family: 'PingFang SC';
    font-weight: 600;
    color: #FFFFFF;
    font-size: pw(16);
    line-height: pw(45);
    text-align: left;
    margin-left: pw(20);
  }

  .circle2 {
    display: none;
  }

  .bear {
    position: absolute;
    transform: translate(-50%, 0);
    left: 50%;
    top: pw(145);
    width: pw(176);
    height: vh(170);
    object-fit: contain;
  }

  }
  </style>
