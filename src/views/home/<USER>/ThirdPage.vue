<template>
  <div class="first-page">
    <div class="circle1"></div>
    <!-- <img class="bg1" :src="FirstDeco" alt="" /> -->
    <!-- <svg-icon icon-class="bingo" class="bg2" /> -->
    <div v-if="showAnimate" class="title animate__animated animate__fadeInDown">企业文化</div>
    <div v-if="showAnimate" class="subtitle animate__animated animate__delay-200 animate__fadeInDown">以创新性为引领，以科技创新为基础，以科学系统性为保障，共同推动公司可持续发展。
    </div>
    <div class="circle2"></div>
    <!-- <svg-icon v-if="showAnimate" icon-class="bear" class="bear animate__animated animate__fadeInRightBig" /> -->
    <div v-if="showAnimate" class="text-group animate__fadeInLeftBig animate__animated animate__delay-200">
      <div class="text-item">创新性</div>
      <div class="text-item">权威性</div>
      <div class="text-item">系统性</div>
      <div class="text-item">严谨性</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showAnimate: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    return {
    }
  }
}
</script>

    <style lang="scss" scoped>
    @media screen and (min-width: 769px) {
    .first-page {
        min-width: 1080px;
        height: 100%;
        // background: linear-gradient(90deg, #330867 0%, #30CFD0 100%);
        background: url('../../../assets/images/home/<USER>') no-repeat center/cover;
        // background-size: 100% 100%;
        position: relative;
        overflow: hidden;
    }

    .bg1 {
        position: absolute;
        width: vh(494);
        height: vh(494);
        left: vw(50);
        top: vh(115);
        object-fit: contain;
    }

    .text-bg {
      position: absolute;
      width: vh(547);
      height: vh(178);
      left: vw(175);
      top: vh(225);
      object-fit: contain;
      background: #fff;
      opacity: 0.1;
      border-radius: 0 0 vh(200) 0;
    }
    .bg2 {
        position: absolute;
        width: vh(550);
        height: vh(274);
        left: vw(825);
        top: vh(485);
        object-fit: contain;
        display: none;
    }

    .title {
        position: absolute;
        top: vh(204);
        left: vw(745);
        font-family: 'PingFang SC';
        font-weight: 600;
        color: #FFFFFF;
        font-size: vh(40);
    }
     .text-group{
        position: absolute;
        top: vh(455);
        left: vw(745);
        font-family: 'PingFang SC';
        color: #FFFFFF;
        width: vh(420);
        font-size: vh(21);
        text-align: left;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: vh(30);
        .text-item:nth-child(1){
            width: vh(188);
            height: vh(51);
            background: linear-gradient(90deg, #65BD60 0%, #5AC1A8 25%, #3EC6ED 50%);
            border-radius: vh(25);
            text-align: center;
            line-height: vh(51);
            font-weight: 600;
            font-size: vh(15);
        }
        .text-item:nth-child(2){
            width: vh(188);
            height: vh(51);
            background: linear-gradient(90deg, #3B41C5 0%, #A981BB 49%, #FFC8A9 100%);
            border-radius: vh(25);
            text-align: center;
            line-height: vh(51);
            font-weight: 600;
            font-size: vh(15);
        }
        .text-item:nth-child(3){
            width: vh(188);
            height: vh(51);
            background: linear-gradient(90deg, #FF5858 0%, #F09819 100%);
            border-radius: vh(25);
            text-align: center;
            line-height: vh(51);
            font-weight: 600;
            font-size: vh(15);
        }
        .text-item:nth-child(4){
            width: vh(188);
            height: vh(51);
            background: linear-gradient(90deg, #209CFF 0%, #68E0CF 100%);
            border-radius: vh(25);
            text-align: center;
            line-height: vh(51);
            font-weight: 600;
            font-size: vh(15);
        }
     }
    .subtitle {
        position: absolute;
        top: vh(305);
        left: vw(745);
        font-family: 'PingFang SC';
        color: #FFFFFF;
        width: vh(500);
        font-size: vh(21);
        text-align: left;
    }

    .circle1 {
      position: absolute;
      width: vh(464);
      height: vh(464);
      left: vw(-234);
      top: vh(432);
      opacity: 0.1;
      border: 0.74961px solid #C7D9FF;
      border-radius: 50%;
    }

    .circle2 {
        position: absolute;
        width: vh(810);
        height: vh(810);
        right: vw(-256);
        bottom: vh(-131);
        opacity: 0.2;
        border: 0.74961px solid #FFEACB;
        border-radius: 50%;
        display: none;
    }

    .bear {
        position: absolute;
        left: vw(950);
        top: vh(331);
        width: vh(340);
        height: vh(326);
        object-fit: contain;
        display: none;
    }
    }

    @media screen and (max-width: 768px) {
    .animate__animated {
      animation: none;
    }

    .first-page {
        width: 100%;
        height: pw(350);
        background: url('../../../assets/images/home/<USER>') no-repeat center/cover;
        position: relative;
        overflow: hidden;
    }

    .circle1 {
      display: none;
    }

    .bg1 {
      width: pw(177);
      height: pw(177);
      object-fit: contain;
      position: absolute;
      top: pw(18);
      left: pw(15);
    }

    .bg2 {
      width: pw(256);
      height: pw(127);
      object-fit: contain;
      position: absolute;
      transform: translate(-50%, 0);
      left: 50%;
      bottom: pw(5);
    }

    .title {
      width: 100%;
      position: absolute;
      transform: translate(-50%, 0);
      top: pw(40);
      left: 50%;
      font-family: 'PingFang SC';
      font-weight: 600;
      color: #FFFFFF;
      font-size: pw(32);
      line-height: pw(45);
    }

    .subtitle {
      width: 100%;
      position: absolute;
      transform: translate(-50%, 0);
      top: pw(98);
      left: 50%;
      font-family: 'PingFang SC';
      font-weight: 600;
      color: #FFFFFF;
      font-size: pw(16);
      line-height: pw(25);
      text-align: left;
      margin-left: pw(20);
    }

    .circle2 {
      display: none;
    }

    .bear {
      position: absolute;
      transform: translate(-50%, 0);
      left: 50%;
      top: pw(145);
      width: pw(176);
      height: vh(170);
      object-fit: contain;
    }

    .text-group {
      width: 100%;
      padding: 0 pw(20);
      position: absolute;
      top: pw(180);
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-wrap: wrap;
      gap: pw(15);
      justify-content: center;

      .text-item {
        width: pw(150);
        height: pw(40);
        border-radius: pw(20);
        font-size: pw(14);
        line-height: pw(40);
        text-align: center;
        font-weight: 600;

        &:nth-child(1) {
          background: linear-gradient(90deg, #65BD60 0%, #5AC1A8 25%, #3EC6ED 50%);
        }
        &:nth-child(2) {
          background: linear-gradient(90deg, #3B41C5 0%, #A981BB 49%, #FFC8A9 100%);
        }
        &:nth-child(3) {
          background: linear-gradient(90deg, #FF5858 0%, #F09819 100%);
        }
        &:nth-child(4) {
          background: linear-gradient(90deg, #209CFF 0%, #68E0CF 100%);
        }
      }
    }
    }
    </style>
