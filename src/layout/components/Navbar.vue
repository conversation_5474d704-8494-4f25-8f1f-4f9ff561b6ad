<template>
  <div class="navbar flex" :class="[{ trans: isTransparent }]">
    <div class="navbar-content">
      <!-- 网站logo -->
      <!-- <router-link v-if="isFontTransparent" to="/" tag="div"> -->
      <router-link v-if="isFontTransparent" to="/home" tag="div">
        <svg-icon icon-class="logo-white" class="logo-trans" />
      </router-link>
      <router-link v-else to="/" tag="div">
        <svg-icon icon-class="logo-black" class="logo" />
      </router-link>
      <!-- 电脑端菜单 -->
      <ul class="nav-ul flex" :class="[{ trans: isFontTransparent }]">
        <li
          to="/home"
          :class="{
            'router-link-active': $route.fullPath === '/',
          }"
          @click="navigate('/')"
        >
          关于我们
        </li>
        <li
          class="has-submenu"
          :class="{
            'router-link-active': $route.fullPath.indexOf('/product') > -1,
          }"
          @click="navigate('/product')"
        >
          产品中心
        </li>
        <li
          :class="{
            'router-link-active': $route.fullPath.indexOf('/research') > -1,
          }"
        >
          AI创新中心
        </li>
        <li
          to="download"
          :class="{
            'router-link-active': $route.fullPath.indexOf('/download') > -1,
          }"
          class="has-submenu"
          @click="navigate('/download')"
        >
          下载
        </li>
      </ul>
      <!-- h5端菜单按钮 -->
      <router-link to="/" tag="div">
        <svg-icon icon-class="logo-black" class="menu-logo" />
      </router-link>
      <div class="menu-btn" @click="changeMenu()">
        <svg-icon icon-class="navbar-menu" class="navbar-btn" />
      </div>
      <!-- h5菜单 -->
      <van-popup
        v-model="showMenu"
        position="right"
        :style="{ width: '80%', overflowY: 'clip' }"
      >
        <div class="menu-h5">
          <ul>
            <li class="flex">
              <div @click="closeMenuNotSolution('/')">
                <svg-icon icon-class="logo-black" class="menu-logo-inner" />
              </div>
              <i
                class="icon-position el-icon-close"
                style="color: black"
                @click="changeMenu()"
              ></i>
            </li>
            <li
              :class="{
                'router-link-active': $route.fullPath.indexOf('/') > -1,
              }"
              @click="closeMenuNotSolution('/')"
            >
              关于我们
            </li>
            <div class="menu-h5-line"></div>
            <li
              :class="{
                'router-link-active': $route.fullPath.indexOf('/product') > -1,
              }"
              @click="closeMenuNotSolution('/product')"
            >
              产品中心
            </li>
            <div class="menu-h5-line"></div>
            <li
              :class="{
                'router-link-active': $route.fullPath.indexOf('/research') > -1,
              }"
            >
              AI创新中心
            </li>
            <div class="menu-h5-line"></div>
            <li
              :class="{
                'router-link-active':
                  $route.fullPath.indexOf('/download') > -1,
              }"
              @click="closeMenuNotSolution('/download')"
            >
              下载
            </li>
          </ul>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Popup, Toast } from 'vant'
import 'vant/lib/popup/style'
import 'vant/lib/toast/style'

Vue.use(Popup, Toast)

export default {
  name: 'CuiyaNavbar',
  data () {
    return {
      isTransparent: false,
      isFontTransparent: false,
      showMenu: false,
      showSolution: false,
      showProduct: false,
      showCourse: false
    }
  },
  methods: {
    goBingoMate () {
      window.open('https://bingomate.com', '_blank')
    },
    goBingoBook (type) {
      window.open(`https://bingobook.cn/#/${type}`, '_blank')
    },
    goBingoClass (type) {
      window.open(`https://bingoclass.com.cn/#/${type}`, '_blank')
    },
    setTrans (type) {
      if (type === 0) {
        this.isTransparent = false
      } else {
        this.isTransparent = true
      }
    },
    setFontTrans (type) {
      if (type === 0) {
        this.isFontTransparent = false
      } else {
        this.isFontTransparent = true
      }
    },
    noOpen () {
      this.$toast('敬请期待', {
        position: 'center',
        duration: '2000'
      })
    },
    gotouser () {
      // const { href } = this.$router.resolve({
      //   path: `/course/schedule`
      // })
      // setTimeout(function() {
      //   window.open(href, '_blank')
      // })
      this.$router.push({
        path: '/course/schedule'
      })
    },
    // 打开/关闭菜单
    changeMenu (navigation) {
      this.showMenu = !this.showMenu
      this.navigate(navigation)
    },
    // 打开/关闭解决方案
    changeShowSolution () {
      this.showSolution = !this.showSolution
    },
    // 打开/关闭产品介绍
    changeShowProduct () {
      this.showProduct = !this.showProduct
    },
    // 关闭菜单和解决方案
    closeMenuNotSolution (navigation) {
      this.showMenu = false
      this.showSolution = false
      this.navigate(navigation)
    },
    navigate (navigation) {
      if (!navigation) {
        return
      }
      const hash = window.location.hash
      const host = window.location.host
      if (
        (host.indexOf('t.cuiya.cn') > -1 || host.indexOf('x.cuiya.cn') > -1) &&
        navigation.indexOf('/home') > -1
      ) {
        location.href = 'https://cuiya.cn/'
        return
      }
      if (hash.indexOf(navigation) > -1) {
        this.$router.go(0)
      } else {
        this.$router.push({ path: navigation })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {
  .navbar {
    position: fixed;
    left: 0;
    top: 0;
    height: 80px;
    width: 100vw;
    background: #ffffff;
    box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.04);
    z-index: 500;

    &.trans {
      background: transparent;
      box-shadow: none;
    }
  }

  .navbar-content {
    // flex: 0 1 1000px;
    width: vw(1000);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .logo {
      flex-shrink: 0;
      width: 130px;
      height: auto;
      background: url("~assets/images/logo-black.svg") center center no-repeat;
      background-size: contain;
      cursor: pointer;
    }

    .logo-trans {
      flex-shrink: 0;
      width: 130px;
      height: auto;
      background: url("~assets/images/logo-white.svg") center center no-repeat;
      background-size: contain;
      cursor: pointer;
    }

    .nav-ul {
      flex-shrink: 0;
      height: 50px;
      color: #141414;
      padding-left: 50%;
      li {
        position: relative;
        margin-right: 45px;
        line-height: 50px;
        font-size: 14px;
        color: #050505;
        cursor: pointer;

        &:nth-last-of-type(1) {
          margin-right: 0px;
        }

        &.router-link-active {
          font-weight: bold;
          text-decoration: underline;
          text-underline-offset: 10px;
        }
        &.router-link-active1 {
          font-weight: bold;
        }
        &:hover {
          font-weight: bold;
        }
      }

      &.trans {
        li {
          color: white;
        }
      }

      .submenu {
        padding-top: 12px;
        display: none;
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translate(-50%, 100%);

        .submenu__top {
          position: relative;
          height: 0px;
          border-radius: 3px;
          background: #ffffff;

          &::after {
            content: "";
            position: absolute;
            top: 0px;
            left: 50%;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 6px 6px 6px;
            border-color: transparent transparent #ffffff transparent;
            transform: translate(-50%, -100%);
            filter: drop-shadow(0px -1px 1px rgba(0, 0, 0, 0.34));
          }
        }

        &__content {
          padding: 12px;
          min-width: 100px;
          background: #ffffff;
          box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.34);
          border-radius: 5px;
          text-align: center;
          white-space: nowrap;
        }

        li {
          padding: 8px 0;
          margin: 0;
          font-size: 14px;
          font-weight: normal;
          line-height: 20px;
          color: #141414;

          &:nth-last-of-type(1) {
            margin: 0;
          }

          &.router-link-active {
            color: #2d9cdb;
            font-weight: bold;
          }

          &:hover {
            color: #2d9cdb;
          }
        }
      }

      .has-submenu {
        &:hover {
          .submenu {
            display: block;
            &:hover {
              display: block;
            }
          }
        }
      }

      &.trans {
        color: #ffffff;
      }
    }
  }

  .menu-logo {
    display: none;
  }

  .menu-logo-trans {
    display: none;
  }

  .menu-btn {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    position: fixed;
    left: 0;
    top: 0;
    height: 64px;
    width: 100vw;
    background: #ffffff;
    box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.04);
    z-index: 500;

    &.trans {
      background: transparent;
      box-shadow: none;
    }
  }

  .nav-ul {
    display: none;
  }

  .navbar-content {
    flex: 0 1 1200px;
    padding: 0 15px;
    background: white;
    display: flex;
    align-items: center;

    .logo {
      display: none;
    }

    .logo-trans {
      display: none;
    }

    .has-submenu {
      display: none;
    }
  }

  .menu-logo {
    width: 106px;
    background-size: cover;
  }

  .menu-btn {
    width: 20px;
    height: 20px;
    margin-left: auto;

    .navbar-btn {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .menu-h5 {
    width: 100%;
    height: 100vh;
    background-color: white;
    padding-left: 15px;

    ul {
      li {
        padding: 20px 15px 20px 0;
        text-align: start;
        font-size: 18px;
        color: rgba(20, 20, 20, 1);
        font-weight: 500;
      }

      .menu-logo-inner {
        width: 106px;
        height: 30px;
      }

      .icon-position {
        margin: auto 0 auto auto;
      }

      .router-link-active {
        color: rgba(45, 156, 219, 1);
        font-weight: 500;
      }
    }

    .solution-nav {
      width: 33%;
      padding: 20px 0 0 0;
      font-size: 14px;
      font-family: "PingFang SC";
      font-weight: 400;
      color: #4f4f4f;
      line-height: 20px;
    }

    &-line {
      width: 100%;
      // margin-left: 20px;
      height: 1px;
      border: 1px solid rgba(151, 151, 151, 0.13);
      order: 13;
      flex-grow: 0;
    }
  }
}
</style>
