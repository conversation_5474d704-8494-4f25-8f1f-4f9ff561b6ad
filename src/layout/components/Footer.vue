<template>
  <div class="footer">
    <!-- 电脑浏览器布局 -->
    <div class="footer-container computer">
      <div class="footer-content flex justify-between align-end pb30">
        <div class="left">
          <svg-icon icon-class="logo-white" class="logo mb30" />
          <div class="flex mb30">
            <svg-icon icon-class="email" class="email mr5" />
            <div class="f15">邮箱：<EMAIL></div>
          </div>
          <div class="flex">
            <svg-icon icon-class="phone" class="phone mr5" />
            <div class="f15">电话：************</div>
          </div>
        </div>
        <!-- <div class="center">
          <div class="flex justify-center mb45">
            <router-link to="/policy">
              <div class="f15 pointer mr60">相关政策</div>
            </router-link>
            <router-link to="/news">
              <div class="f15 pointer mr60">媒体报道</div>
            </router-link>
            <router-link to="/about">
              <div class="f15 pointer">关于我们</div>
            </router-link>
          </div>
        </div> -->
        <div class="right">
          <div class="flex justify-center" style="width: 82px;margin-left: auto;">
            <div class="f18 mb30" style="text-align:center; ">相关网站</div>
            <div>
              <a href="https://binguoketang.com" target="_blank">缤果课堂</a>
              <a href="https://bingobook.com.cn" target="_blank">缤果数字教材</a>
              <a href="https://bingomate.com" target="_blank">缤果学伴</a>
              <a href="https://cuiya.cn" target="_blank">四川萃雅</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- h5布局 -->
    <div class="footer-container h5">
      <div class="footer-content">
        <div class="footer-info flex flex-col justify-between">
          <div class="flex margin-bottom-10 align-center">
            <div class="red-line"></div>
            <div class="label">联系我们：</div>
            <div class="content"><EMAIL></div>
          </div>
          <div class="flex align-center">
            <div class="red-line"></div>
            <div class="label">客服热线：</div>
            <div class="content">************</div>
          </div>
          <div class="flex mt10">
            <div class="red-line"></div>
            <div class="label">相关网站：</div>
            <div>
              <a href="https://binguoketang.com" target="_blank">缤果课堂</a>
              <a href="https://bingobook.com.cn" target="_blank">缤果数字教材</a>
              <a href="https://bingomate.com" target="_blank">缤果学伴</a>
              <a href="https://cuiya.cn" target="_blank">四川萃雅</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部备案号 -->
    <div class="footer-icp">
      <a
        href="https://beian.miit.gov.cn"
        target="_blank"
      >{{`ICP备案号：${ record }`}}</a>
      <span style="margin-left: 50px;">©2009-2025 缤果科技</span>
      <span style="margin-left: 50px;">北京英华高科科技有限公司 版权所有</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CuiyaFooter',
  computed: {
    record () {
      const host = window.location.host
      if (host.indexOf('cuiyaedu.com') > -1) {
        return '蜀ICP备2021023754号-2'
      } else if (host.indexOf('cuiyaedu.cn') > -1) {
        return '蜀ICP备2021023754号-1'
      } else if (host.indexOf('cuiya.cn') > -1) {
        return '蜀ICP备2021023754号-3'
      } else if (host.indexOf('bingomate.cn') > -1) {
        return '京ICP备18001616号-9'
      } else {
        return '京ICP备18001616号-8'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {
.h5 {
  display: none;
}

.footer {
  width: 100%;
}

.footer-container {
  width: 100vw;
  height: 285px;
  background: #060219;
}

.footer-content {
  width: 1000px;
  margin: 0 auto;
  height: 100%;
  color: white;

  .left {
    flex: 1;
    text-align: start;

    .logo {
      width: 150px;
      object-fit: contain;
      margin-top: 20px;
    }

    .email {
      width: 15px;
      height: 15px;
      object-fit: contain;
    }

    .phone {
      width: 15px;
      height: 15px;
      object-fit: contain;
    }
  }

  .center {
    flex: 1;
  }

  .right {
    flex: 1;
    text-align: end;
    white-space:nowrap;
    a{
      display: block;
      margin-right: 10px;
      margin-bottom: 20px;
      text-align: left;
      margin-left: 20px;
    }
  }
}

.footer-icp {
  border-top: 0.5px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  height: 40px;
  background: #060219;
  font-size: 12px;
  color: #ffffff;
  line-height: 40px;
  text-align: center;
}

.some-link {
  margin-bottom: 34px;
  padding: 18px 0;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.2);

  li {
    width: 80px;
    margin-right: 42px;
    cursor: pointer;

    &:hover {
      font-weight: bold;

      i {
        padding-left: 2px;
        font-weight: 700;
      }
    }
  }
}

.footer-info {
  height: 95px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  line-height: 20px;

  .label {
    position: relative;
    padding-left: 10px;
    padding-right: 42px;

    &::before {
      position: absolute;
      left: 0;
      top: 50%;
      content: '';
      width: 2px;
      height: 14px;
      background: #c32136;
      transform: translate(0, -50%);
    }
  }

  .content {
    .friends {
      color: #ff6d6d;

      li {
        margin-right: 28px;
      }
    }
  }

  .content-phone {
    display: none;
  }
}
}

@media screen and (max-width: 768px) {
  .computer {
    display: none;
  }

  .footer {
    width: 100%;
    height: pw(318);
    padding: 0 pw(15);
    background: rgba(6, 2, 25, 1);
    display: flex;
    flex-direction: column;
  }

  .footer-container {
    width: 100%;
    height: pw(282);
    a{
      display: block;
      text-align: left;
      margin-bottom: pw(10);
    }
  }

  .footer-content {
    width: 100%;
    padding-top: pw(24);

    .qr-code {
      padding-top: 25px;
      margin: 0 auto;
      .qr {
        height: pw(90);
        width: pw(90);
        background-size: contain;
        padding: pw(6);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 3px;
      }

      .tip {
        margin-top: pw(3);
        font-size: pw(12);
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: pw(20);
        text-align: center;
      }
    }
  }

  .some-link {
    margin-bottom: pw(20);
    padding-bottom: pw(10);
    font-size: pw(14);
    color: #ffffff;
    line-height: pw(20);
    border-bottom: pw(1) solid rgba($color: #ffffff, $alpha: 0.2);

    li {
      font-family: 'PingFang SC';
      color: #FFFFFF;
      margin-right: pw(38);

      &:hover {
        font-weight: bold;

        i {
          padding-left: 2px;
          font-weight: 700;
        }
      }
    }
  }

  .footer-info {
    font-weight: 400;
    color: #ffffff;
    font-size: pw(14);
    line-height: pw(20);

    .red-line {
        margin-top: pw(2);
        width: pw(2);
        height: pw(12);
        background: rgba(195, 33, 54, 1);
    }

    .label {
      padding-left: pw(2);
      padding-right: pw(15);
    }

    .content {
      .friends {
        color: #ff6d6d;
      }

      .friends2 {
        color: #ff6d6d;
        li {
          margin-right: 34px;
        }
      }
    }

    .margin-bottom-10 {
      margin-bottom: 10px;
    }

    .margin-right-22 {
      margin-right: 22px;
    }
  }

  .footer-icp {
    width: 100%;
    height: pw(35);
    line-height: pw(35);
    font-size: pw(14);
    color: #ffffff;
    text-align: center;
    border-top: 0.5px solid rgba(255, 255, 255, 0.2);
  }
}
</style>
