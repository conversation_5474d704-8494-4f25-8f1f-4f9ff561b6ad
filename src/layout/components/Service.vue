<template>
  <div class="container">
    <!-- 电脑端布局 -->
    <div class="service-container computer">
      <div class="service-box flex flex-col justify-center items-center">
        <div class="icon">
          <!-- <img
            src="@/assets/images/cuiya/nav/service.png"
            class="w-full h-full object-contain"
          /> -->
        </div>
        <div class="text">客服咨询</div>
      </div>
      <div class="qr-box flex flex-col justify-center items-center">
        <div class="icon">
          <!-- <img
            src="@/assets/images/cuiya/nav/service-qr.png"
            class="w-full h-full object-contain"
          /> -->
        </div>
        <div class="text">微信扫一扫</div>
      </div>
    </div>
    <!-- h5布局 -->
    <div class="service-container h5">
      <div class="service-box flex flex-col justify-center items-center" @click="ServiceContact()">
        <div class="icon">
          <!-- <img
            src="@/assets/images/cuiya/nav/service.png"
            class="w-full h-full object-contain"
          /> -->
        </div>
        <div class="text">客服咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CuiyaService',
  methods: {
    ServiceContact () {
      window.location.href = 'https://work.weixin.qq.com/kfid/kfcc1de3ecf733aaaa6'
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 769px) {
  .h5 {
    display: none;
  }

  .service-container {
    position: fixed;
    right: 60px;
    bottom: 38%;
    height: 100px;
    width: 100px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.04);
    border-radius: 15px;
    border: 1px solid rgba(151, 151, 151, 0.29);
    background: #ffffff;
    z-index: 99;
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
    transform-origin: bottom right;
    overflow: hidden;
  }

  .service-box {
    width: 100%;
    height: 100%;
    cursor: pointer;

    .icon {
      height: 40px;
      width: 40px;
    }

    .text {
      padding-top: 8px;
      font-size: 16px;
      color: #5d5d5d;
      line-height: 22px;
    }
  }

  .qr-box {
    display: none;
    width: 100%;
    height: 100%;
    cursor: pointer;

    .icon {
      height: 180px;
      width: 180px;
    }

    .text {
      padding-top: 8px;
      font-size: 16px;
      color: #5d5d5d;
      line-height: 22px;
    }
  }

  .service-container:hover {
    height: 250px;
    width: 200px;

    .service-box {
      display: none;
    }

    .qr-box {
      display: flex;
    }
  }
}

@media screen and (max-width: 768px) {
  .computer {
    display: none;
  }

  .service-container {
    position: fixed;
    right: 17px;
    bottom: 20%;
    height: 80px;
    width: 80px;
    background: #FFFFFF;
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 40px;
    border: 1px solid rgba(151, 151, 151, 0.29);
    z-index: 99;
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
    transform-origin: bottom right;
    overflow: hidden;
  }

  .service-box {
    width: 100%;
    height: 100%;
    cursor: pointer;

    .icon {
      height: 38px;
      width: 34px;
    }

    .text {
      padding-top: 4px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #5D5D5D;
      line-height: 17px;
    }
  }

  .qr-box {
    display: none;
    width: 100%;
    height: 100%;
    cursor: pointer;

    .icon {
      height: 180px;
      width: 180px;
    }

    .text {
      padding-top: 8px;
      font-size: 16px;
      color: #5d5d5d;
      line-height: 22px;
    }
  }
}
</style>
