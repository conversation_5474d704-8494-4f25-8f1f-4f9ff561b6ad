<template>
  <div class="cuiya-wrapper">
    <Navbar ref="cuiyaNav" />
    <router-view />
    <Footer v-if="footerState" />
    <Service v-if="false" />
  </div>
</template>

<script>
import Footer from './components/Footer.vue'
import Navbar from './components/Navbar.vue'
import Service from './components/Service.vue'

export default {
  name: 'CuiyaLayout',
  components: { Navbar, Footer, Service },
  provide () {
    return {
      setNavState: this.setNavState,
      setNavFontState: this.setNavFontState,
      setFooterState: this.setFooterState
    }
  },
  data () {
    return {
      footerState: true
    }
  },
  methods: {
    setNavState (type) {
      if (this.$refs.cuiyaNav) {
        this.$refs.cuiyaNav.setTrans(type)
      }
    },
    setNavFontState (type) {
      if (this.$refs.cuiyaNav) {
        this.$refs.cuiyaNav.setFontTrans(type)
      }
    },
    setFooterState (footerState) {
      this.footerState = footerState
    }
  }
}
</script>

<style lang="scss">
html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

.cuiya-wrapper {
  overflow: hidden;
  .app-container {
    margin: 0 auto;
    padding: 80px 0 0 0;
    min-height: calc(100vh - 325px);
    width: 1200px;
  }
}

@media screen and (max-width: 768px) {
  body {
    padding-top: 64px;
  }

  .cuiya-wrapper {
    overflow: hidden;
    .app-container {
      min-height: calc(100vh - 64px - #{pw(318)});
    }
  }
}
</style>
