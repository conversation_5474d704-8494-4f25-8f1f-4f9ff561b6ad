import request from '@/utils/request'

export function goodsCategoryList (params) {
  return request({
    url: '/api/goods/vt/goodsCategoryList',
    method: 'get',
    params
  })
}

export function getNewsList (params) {
  return request({
    url: 'Home/InformationApi/list',
    method: 'get',
    params,
    urlType: 'admin'
  })
}

export function getNewsDetail (params) {
  return request({
    url: 'Home/InformationApi/detail',
    method: 'get',
    params,
    urlType: 'admin'
  })
}

export function getLastAppVersion (headers) {
  return request({
    url: 'api/v2/comm/vt/getLastAppVersion',
    method: 'get',
    urlType: 'api',
    headers
  })
}
