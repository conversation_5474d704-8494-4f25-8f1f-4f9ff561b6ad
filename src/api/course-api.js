import request from '@/utils/request'
// 获取课程包系列-主题下的课程
export function getCoursePackageList (params) {
  return request({
    url: 'api/v2/course/vt/getCoursePackageList',
    method: 'get',
    params
  })
}

export function getCourseSeriesList () {
  return request({
    url: 'api/v2/course/vt/getCourseSeriesList',
    method: 'get'
  })
}

// 获取课程包系列-主题下的课程
export function getCoursePackageInfo (params) {
  return request({
    url: 'api/v2/course/vt/getCoursePackageInfo',
    method: 'get',
    params
  })
}

export function getCourseList (data) {
  return request({
    url: 'api/course/vt/getCourseList',
    method: 'get',
    params: data,
    urlType: 'qinguo'
  })
}

export function getCourseChapterInfo (data) {
  return request({
    url: 'api/course/vt/getCourseChapterInfo',
    method: 'get',
    params: data,
    urlType: 'qinguo'
  })
}

export function getCourseInfo (data) {
  return request({
    url: 'api/course/vt/getCourseInfo',
    method: 'get',
    params: data,
    urlType: 'qinguo'
  })
}
