import Vue from 'vue'
import VueRouter from 'vue-router'
Vue.use(VueRouter)
const Layout = () => import('@/layout')

const routes = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        component: () => import('@/views/home'),
        name: 'Home'
      },
      {
        path: 'product',
        component: () => import('@/views/product'),
        name: 'Product'
      },
      {
        path: 'download',
        component: () => import('@/views/download'),
        name: 'Download'
      },
      {
        path: 'bingolive',
        component: () => import('@/views/bingoLive'),
        name: 'BingoLive'
      },
      {
        path: 'bingomate',
        component: () => import('@/views/bingoMate'),
        name: 'BingoMate'
      },
      {
        path: 'bingoplus',
        component: () => import('@/views/bingoPlus'),
        name: 'BingoPlus'
      }
    ]
  }
]
const router = new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ x: 0, y: 0 }),
  routes
})

export default router
