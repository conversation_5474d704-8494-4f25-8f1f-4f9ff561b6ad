import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'normalize.css/normalize.css'
import '@/styles/index.scss' // global css
import '@/icons'
import 'element-ui/lib/theme-chalk/index.css'
import { Popover, Row, Col, Icon, Avatar, Message } from 'element-ui'

import 'animate.css'
import VueAnimateOnScroll from 'vue-animate-onscroll'

import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'

import 'fullpage.js/vendors/scrolloverflow'
import VueFullPage from 'vue-fullpage.js'
import './permission'
Vue.prototype.$message = Message
Vue.use(Popover)
Vue.use(Row)
Vue.use(Col)
Vue.use(Icon)
Vue.use(Avatar)

Vue.config.productionTip = false
Vue.use(VueAwesomeSwiper)

Vue.use(VueAnimateOnScroll)
Vue.use(VueFullPage)
// 全局事件总线
Vue.prototype.$bus = new Vue()
Vue.prototype.$src = process.env.VUE_APP_OSS_SRC
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
