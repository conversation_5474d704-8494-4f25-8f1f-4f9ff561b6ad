import router from './router'
const href = window.location.href
const host = window.location.host

router.beforeEach((to, from, next) => {
  if (href.indexOf('datacenter') > -1) {
    location.href = 'https://data.cuiya.cn'
  }
  if (host.indexOf('t.cuiya.cn') > -1) {
    if (to.path.indexOf('home') > -1) {
      next({ path: '/download/teacher' })
    }
  }
  if (host.indexOf('x.cuiya.cn') > -1) {
    if (to.path.indexOf('home') > -1) {
      next({ path: '/download/student' })
    }
  }
  if (to.meta.title) {
    // if (document.body.clientWidth < 769) {
    //   document.title = to.meta.title
    // } else {
    //   document.title = '萃雅教育-科技打造创新教育新模式'
    // }
    document.title = to.meta.title
  }
  if (to.meta.keywords) {
    const keywords = document.querySelectorAll('meta')[4]
    if (keywords) {
      keywords.setAttribute('content', to.meta.keywords)
    }
  }
  if (to.meta.description) {
    const description = document.querySelectorAll('meta')[5]
    if (description) {
      description.setAttribute('content', to.meta.description)
    }
  }
  next()
})
