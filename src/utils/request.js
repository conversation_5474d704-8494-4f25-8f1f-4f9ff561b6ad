import axios from 'axios'
// import { Message } from 'element-ui'
// import store from '@/store'
// import { getToken } from '@/utils/auth'
// import { Toast } from 'vant'

// create an axios instance
const service = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 120000, // request timeout
  params: {
    token: ''
  }
})
// request interceptor
service.interceptors.request.use(
  (config) => {
    switch (config.urlType) {
      case 'admin':
        config.url = process.env.VUE_APP_ADMIN_URL + config.url
        break
      case 'qinguo':
        config.url = process.env.VUE_APP_QINGUO_API + config.url
        break
      case 'api':
        config.url = process.env.VUE_APP_API_URL + config.url
        if (!config.url.includes('getLastAppVersion')) {
          config.headers['app'] = 'Tradition'
          config.headers['appType'] = 'WEB'
          config.headers['appVersion'] = '1.0.0'
        }
        break
      default:
        config.url = process.env.VUE_APP_API_URL + config.url
    }
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const res = response.data
    // if the custom code is not 20000, it is judged as an error.
    if (+res.code !== 200) {
      return Promise.reject(res)
      // return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  (error) => {
    console.log('err' + error) // for debug
    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    // if (['Network Error'].indexOf(error.message) > -1) {
    //   Toast.fail('网络故障，请检测网络')
    // } else {
    //   Toast.fail(error.message)
    // }
    return Promise.reject(error)
  }
)

export default service
