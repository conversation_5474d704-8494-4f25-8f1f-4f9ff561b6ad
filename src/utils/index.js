// 防抖 immediate 是否开始立即执行

// 使用方式 methods:
// click1: debounce(async function () {
//   await {data} = getInfo()
//   console.log('防抖')
// }, 3000, true)

export function debounce (func, wait = 3000, immediate = false) {
  let timeout

  return function () {
    const context = this
    const args = arguments

    if (timeout) clearTimeout(timeout) // timeout 不为null
    if (immediate) {
      const callNow = !timeout // 第一次会立即执行，以后只有事件执行后才会再次触发
      timeout = setTimeout(function () {
        timeout = null
      }, wait)
      if (callNow) {
        func.apply(context, args)
      }
    } else {
      timeout = setTimeout(function () {
        func.apply(context, args)
      }, wait)
    }
  }
}

// 节流
// 使用方式 methods:
// click2: throttle(function() {
//   console.log('节流')
// }, 2000)
export function throttle (fn, wait = 3000) {
  var timer = null
  return function () {
    var context = this
    var args = arguments
    if (!timer) {
      timer = setTimeout(function () {
        fn.apply(context, args)
        timer = null
      }, wait)
    }
  }
}

export function changeTitle (title) {
  const dom = document.querySelector('title')
  if (dom) {
    dom.innerText = title
  }
}

export function getDocumentTop () {
  var scrollTop = 0
  var bodyScrollTop = 0
  var documentScrollTop = 0
  if (document.body) {
    bodyScrollTop = document.body.scrollTop
  }
  if (document.documentElement) {
    documentScrollTop = document.documentElement.scrollTop
  }
  scrollTop =
    bodyScrollTop - documentScrollTop > 0
      ? bodyScrollTop
      : documentScrollTop
  return scrollTop
}

export function getScrollHeight () {
  var scrollHeight = 0
  var bodyScrollHeight = 0
  var documentScrollHeight = 0
  if (document.body) {
    bodyScrollHeight = document.body.scrollHeight
  }

  if (document.documentElement) {
    documentScrollHeight = document.documentElement.scrollHeight
  }
  scrollHeight =
    bodyScrollHeight - documentScrollHeight > 0
      ? bodyScrollHeight
      : documentScrollHeight
  return scrollHeight
}

export function getWindowHeight () {
  var windowHeight = 0
  if (document.compatMode === 'CSS1Compat') {
    windowHeight = document.documentElement.clientHeight
  } else {
    windowHeight = document.body.clientHeight
  }
  return windowHeight
}
