{"name": "cuiya", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:qa": "vue-cli-service build --mode qa", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "^4.1.1", "axios": "^0.23.0", "core-js": "^3.6.5", "element-ui": "^2.15.6", "js-cookie": "2.2.1", "moment": "^2.29.1", "normalize.css": "^8.0.1", "qrcodejs2": "^0.0.2", "swiper": "^5.3.7", "vant": "^2.12.30", "video.js": "^7.11.0", "vue": "^2.6.11", "vue-animate-onscroll": "^1.0.8", "vue-awesome-swiper": "^4.1.0", "vue-fullpage.js": "^0.1.7", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@vue/cli-plugin-babel": "~4.5.11", "@vue/cli-plugin-eslint": "~4.5.11", "@vue/cli-plugin-router": "~4.5.11", "@vue/cli-plugin-vuex": "~4.5.11", "@vue/cli-service": "~4.5.11", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.12.0", "sass-loader": "^8.0.2", "svg-sprite-loader": "^6.0.11", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}