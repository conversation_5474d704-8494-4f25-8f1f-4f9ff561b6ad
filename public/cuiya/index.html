<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-store, must-revalidate">
    <title>萃雅教育</title>
    <link rel="stylesheet" href="./normalize.css" />
    <style>
      .pointer {
        cursor: pointer;
      }
      .w {
        width: 100%;
      }
      .tr {
        text-align: right;
      }
      .flex {
        display: flex;
      }
      .justify-center {
        justify-content: center;
      }
      .justify-between {
        justify-content: space-between;
      }
      .mt40 {
        margin-top: 40px;
      }
      .mb20 {
        margin-bottom: 20px;
      }
      .mr50 {
        margin-right: 50px;
      }
      .mb40 {
        margin-bottom: 40px;
      }

      .mb60 {
        margin-bottom: 60px;
      }
      .bg1 {
        background: url('./img/1.png');
        background-repeat: no-repeat;
        background-size: cover;
        height: 800px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .bg2 {
        background: url('./img/bg-2.png');
        background-repeat: no-repeat;
        background-size: cover;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .content {
        width: 1100px;
      }

      .title-box {
        height: 500px;
      }

      .title1 {
        font-size: 55px;
        margin-bottom: 50px;
      }
      .title2 {
        font-size: 34px;
        margin-bottom: 50px;
      }

      .transparent-bg {
        background-color: rgba(255, 255, 255, 0.2);
        height: 45px;
        width: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        font-size: 16px;
      }

      .btn-1 {
        height: 50px;
      }

      .t-bg {
        position: relative;
        width: 240px;
        height: 280px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.16);
        box-shadow: 0px 4px 49px 12px rgba(255, 255, 255, 0.25) inset;
        padding: 30px 20px 50px 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        text-align: center;
        line-height: 35px;
        font-size: 26px;
      }
      .icon-fix {
        height: 150px;
        position: absolute;
        top: -50px;
        left: 45px;
      }
      .circle {
        width: 55px;
        height: 55px;
        background-color: #ffd401;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #000;
        font-size: 32px;
        font-weight: 500;
        border-radius: 50%;
        flex-shrink: 0;
      }

      .wechat-box {
        position: relative;
      }
      .wechat-box-show {
        display: none;
        position: absolute;
        top: -240px;
        left: -40px;
      }

      .phone-box {
        position: relative;
      }
      .phone-box-show {
        display: none;
        width: 120px;
        background-color: #fff;
        color: #000;
        font-size: 16px;
        padding: 10px 20px;
        position: absolute;
        top: -45px;
        left: -5px;
        border-radius: 5px;
      }
      .phone-box-show-2 {
        display: none;
        width: 240px;
        background-color: #fff;
        color: #000;
        font-size: 16px;
        padding: 10px 20px;
        position: absolute;
        top: 25px;
        left: -120px;
        border-radius: 5px;
      }
    </style>
  </head>
  <body>
    <div class="bg1">
      <div class="content title-box">
        <div class="title1">《综合实践活动课程》双师AI课堂</div>
        <div class="title2">培养学生跨学科创新综合实践能力与素养的课程</div>
        <div class="flex mb40">
          <div class="transparent-bg mr50">双师轻课赋能教学</div>
          <div class="transparent-bg">生动有趣 易懂易学</div>
        </div>
        <div class="flex mb60">
          <div class="transparent-bg mr50">纸数融合一体化</div>
          <div class="transparent-bg">交互式课堂高质高效</div>
        </div>
        <!-- <div class="w flex" style="justify-content: flex-end">
          <div id="wechat-box-1" class="mr50 wechat-box">
            <img class="btn-1" src="./img/btn1.png" />
            <div id="wechat-box-show-1" class="wechat-box-show">
              <img width="240" src="./img/wechat.png" />
            </div>
          </div>
          <div id="phone-box-1" class="phone-box">
            <img class="btn-1" src="./img/btn2.png" />
            <div id="phone-box-show-1" class="phone-box-show">************</div>
          </div>
        </div> -->
      </div>
    </div>
    <div class="bg2">
      <div class="content">
        <div
          style="
            text-align: center;
            font-size: 35px;
            font-weight: 500;
            margin: 150px 0 100px 0;
          "
        >
          下载《综合实践活动双师AI课堂》教学客户端
        </div>
        <div
          class="w flex mt40 mb60"
          style="align-items: center; justify-content: center"
        >
          <div class="transparent-bg" style="height: 90px; width: 300px">
            <div class="circle">1</div>
            <div style="margin-left: 15px">
              <div style="color: #fff; font-size: 20px; margin-bottom: 5px">
                下载客户端
              </div>
              <div style="color: #ffd401; font-size: 14px">
                下方选择电脑端版本
              </div>
            </div>
          </div>
          <img style="height: 10px; margin: 0 10px" src="./img/line.png" />
          <div class="transparent-bg" style="height: 90px; width: 300px">
            <div class="circle">2</div>
            <div style="margin-left: 15px">
              <div style="color: #fff; font-size: 20px; margin-bottom: 5px">
                获取激活码
              </div>
              <div
                id="getphone"
                class="pointer"
                style="
                  color: #ffd401;
                  font-size: 14px;
                  text-decoration-line: underline;
                  position: relative;
                "
              >
                联系客服获取>
                <div id="wechat-box-show-3" class="phone-box-show-2">
                  <img width="240" src="./img/wechat.png" />
                </div>
              </div>
            </div>
          </div>
          <img style="height: 10px; margin: 0 10px" src="./img/line.png" />
          <div class="transparent-bg" style="height: 90px; width: 300px">
            <div class="circle">3</div>
            <div style="margin-left: 15px">
              <div style="color: #fff; font-size: 20px; margin-bottom: 5px">
                课程激活使用
              </div>
              <div
                id="usepdf"
                class="pointer"
                style="
                  color: #ffd401;
                  font-size: 14px;
                  text-decoration-line: underline;
                "
              >
                使用帮助指南>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-center" style="margin-bottom: 80px">
          <div style="margin-right: 150px">
            <a id="win" href="#" target="_blank">
              <img
                id="d-1"
                class="pointer"
                style="width: 200px; height: 200px"
                src="./img/d-1.png"
              />
            </a>
            <div
              style="
                text-align: center;
                font-size: 26px;
                font-weight: 600;
                margin-top: 20px;
              "
            >
              Windows
            </div>
          </div>
          <div>
            <a id="mac" href="#" target="_blank">
              <img
                id="d-2"
                class="pointer"
                style="width: 200px; height: 200px"
                src="./img/d-2.png"
              />
            </a>
            <div
              style="
                text-align: center;
                font-size: 26px;
                font-weight: 600;
                margin-top: 20px;
              "
            >
              MacOS
            </div>
          </div>
        </div>
        <div class="flex justify-center" style="margin-bottom: 80px">
          <div id="wechat-box-2" style="width: 200px;text-align: center;margin-right: 150px;display: flex; justify-content: center;" class="mr50 wechat-box">
            <img
              class="pointer"
              style="width: 180px;"
              src="./img/btn3.png"
            />
            <div id="wechat-box-show-2" class="wechat-box-show">
              <img width="240" src="./img/wechat.png" />
            </div>
          </div>
          <div id="phone-box-2"style="width: 200px;text-align: center;display: flex; justify-content: center;" class="phone-box">
            <img class="pointer" style="width: 150px" src="./img/btn-4.png" />
            <div id="phone-box-show-2" class="phone-box-show">************</div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg2">
      <div class="content">
        <div style="text-align: center" class="mt40 mb20 pointer">
          <!-- <img style="width: 900px" class="mt40 mb20" src="./img/show-1.png" /> -->
          <video width="900" height="500" controls poster="./img/show-1.png" controls="true" controlslist="nodownload">
            <source src="https://static.bingotalk.cn/bingoprd/video/********************************.mp4" type="video/mp4">
            您的浏览器不支持 HTML5 video 标签。
          </video>
        </div>
        <div style="text-align: center">
          全面升级《综合实践活动》系列助学图书为融媒体产品，创设学生感兴趣的数字化教学内容，提高助学图书品质，赋能一线教师教学。
        </div>
        <div class="w flex justify-between mt40 mb60">
          <img style="width: 300px" src="./img/text-1.png" />
          <img style="width: 300px" src="./img/text-2.png" />
          <img style="width: 300px" src="./img/text-3.png" />
        </div>
      </div>
    </div>
    <div class="bg2">
      <div class="content">
        <div
          style="text-align: center; font-size: 50px; margin: 150px 0 100px 0"
        >
          课程特色
        </div>
        <div class="w flex justify-between mt40 mb60">
          <div class="t-bg">
            <img class="icon-fix" src="./img/icon-1.png" />
            双师轻课模式<br>赋能教学全环节
          </div>
          <div class="t-bg">
            <img class="icon-fix" src="./img/icon-2.png" />
            内容生动有趣<br>易懂易学
          </div>
          <div class="t-bg">
            <img class="icon-fix" src="./img/icon-3.png" />
            纸数融合驱动备授课一体化场景
          </div>
          <div class="t-bg">
            <img class="icon-fix" src="./img/icon-4.png" />
            交互式课堂效果 高质高效
          </div>
        </div>
      </div>
    </div>

    <script type="text/javascript" src="./jq.js"></script>
    <script>
      $('#d-1').hover(
        (e) => {
          $('#d-1').attr('src', './img/d-3.png')
        },
        (e) => {
          $('#d-1').attr('src', './img/d-1.png')
        }
      )
      $('#d-2').hover(
        (e) => {
          $('#d-2').attr('src', './img/d-3.png')
        },
        (e) => {
          $('#d-2').attr('src', './img/d-2.png')
        }
      )

      $('#wechat-box-1').hover(
        (e) => {
          $('#wechat-box-show-1').show()
        },
        (e) => {
          $('#wechat-box-show-1').hide()
        }
      )

      $('#phone-box-1').hover(
        (e) => {
          $('#phone-box-show-1').show()
        },
        (e) => {
          $('#phone-box-show-1').hide()
        }
      )

      $('#wechat-box-2').hover(
        (e) => {
          $('#wechat-box-show-2').show()
        },
        (e) => {
          $('#wechat-box-show-2').hide()
        }
      )

      $('#phone-box-2').hover(
        (e) => {
          $('#phone-box-show-2').show()
        },
        (e) => {
          $('#phone-box-show-2').hide()
        }
      )
      $('#getphone').hover(
        (e) => {
          $('.phone-box-show-2').show()
        },
        (e) => {
          $('.phone-box-show-2').hide()
        }
      )

      function getMacUrl(params) {
        $.ajax({
          type: 'get',
          headers: {
            appVersion: '0.0.0',
            app: 'BingoClass',
            appType: 'APP_MAC',
            channel: 'wenxuan'
          },
          url: 'https://api.bingotalk.cn/api/v2/comm/vt/getLastAppVersion',
          // contentType: 'application/json'
          contentType: 'application/x-www-form-urlencoded',
          // 会自动帮我们把返回的数据根据响应头设置的类型进行转换好（也就是会自动转成json对象）
          success: function (response) {
            // console.log(response.data.appUrl)
            $('#mac').attr('href', response.data.appUrl)
          },
          // xhr是ajax对象
          error: function (xhr) {}
        })
      }

      function getWinUrl(params) {
        $.ajax({
          type: 'get',
          headers: {
            appVersion: '0.0.0',
            app: 'BingoClass',
            appType: 'APP_WIN',
            channel: 'wenxuan'
          },
          url: 'https://api.bingotalk.cn/api/v2/comm/vt/getLastAppVersion',
          // contentType: 'application/json'
          contentType: 'application/x-www-form-urlencoded',
          // 会自动帮我们把返回的数据根据响应头设置的类型进行转换好（也就是会自动转成json对象）
          success: function (response) {
            // console.log(response.data.appUrl)
            $('#win').attr('href', response.data.appUrl)
          },
          // xhr是ajax对象
          error: function (xhr) {}
        })
      }

      var pdfUrl = ''

      function getPdfUrl(params) {
        $.ajax({
          type: 'get',
          url: 'https://api.bingotalk.cn/api/v1/dictionary/getConfig?configType=AI_GUIDE_PDF',
          // contentType: 'application/json'
          contentType: 'application/x-www-form-urlencoded',
          // 会自动帮我们把返回的数据根据响应头设置的类型进行转换好（也就是会自动转成json对象）
          success: function (response) {
            pdfUrl = response.data[0].keyValue
          },
          // xhr是ajax对象
          error: function (xhr) {}
        })
      }

      $('#usepdf').click(() => {
        window.open(pdfUrl, '_blank', 'width=600,height=800,left=200,top=100,menubar=0,scrollbars=1,resizable=1,status=1,titlebar=0,toolbar=0,location=1')
      })
      getPdfUrl()
      getMacUrl()
      getWinUrl()

    </script>
  </body>
</html>
