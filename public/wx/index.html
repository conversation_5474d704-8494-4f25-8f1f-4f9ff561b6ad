<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>下载中心</title>
    <style>
      .wx {
        width: 100%;
        height: 100%;
      }
      body, html {
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <script>
      const url = `${location.origin}/#/download/wx`
      //添加iframe标签
      const div = document.createElement('div')
      div.style.width= '100vw'
      div.style.height= '100vh'
      div.innerHTML =
        `<iframe class="wx" src="${url}" frameborder="0"></iframe>`
      document.body.appendChild(div)
    </script>
  </body>
</html>
