<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-store" />
    <title>测试4</title>
    <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
  </head>
  <body>
    <a href="" id="download">超链接下载</a>
    <button id="downloadButon">按钮下载</button>
    <script>
      var vConsole = new VConsole()
      var url = ''
      var fileName = 'bingoBook.apk'
      var xhr = new XMLHttpRequest()
      xhr.open(
        'GET',
        'https://api.qa.bingotalk.cn/api/v2/comm/vt/getLastAppVersion'
      )
      xhr.setRequestHeader('Content-Type', 'application/json')
      xhr.setRequestHeader('appVersion', '0.0.0')
      xhr.setRequestHeader('app', 'BingoBook')
      xhr.setRequestHeader('appType', 'APP_ANDROID')
      xhr.onload = function () {
        if (xhr.status >= 200 && xhr.status < 300) {
          document.getElementById('download').href = JSON.parse(
            xhr.responseText
          ).data.appUrl
          url = JSON.parse(xhr.responseText).data.appUrl
        } else {
          console.error('Error:', xhr.statusText)
        }
      }

      xhr.onerror = function () {
        console.error('Request failed')
      }

      xhr.send()
      function downloadFile() {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob' // 设置响应类型为 blob

        // 处理请求完成后的逻辑
        xhr.onload = function () {
          if (xhr.status === 200) {
            // 创建一个 URL 对象
            const blobUrl = URL.createObjectURL(xhr.response)

            // 创建一个隐藏的 <a> 元素
            const a = document.createElement('a')
            a.href = blobUrl
            a.download = fileName // 指定下载文件名
            document.body.appendChild(a)
            a.click() // 模拟点击下载
            document.body.removeChild(a) // 移除 <a> 元素
            URL.revokeObjectURL(blobUrl) // 释放 blob URL
          } else {
            console.error('文件下载失败', xhr.status, xhr.statusText)
          }
        }

        // 处理错误
        xhr.onerror = function () {
          console.error('AJAX 请求失败')
        }

        // 发送请求
        xhr.send()
      }
      document.getElementById('downloadButon').onclick = downloadFile
    </script>
  </body>
</html>
